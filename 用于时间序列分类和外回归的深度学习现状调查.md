# 用于时间序列分类和外生回归的深度学习：当前综述

NAVID MOHAMMADI FOUMANI, 莫纳什大学, 墨尔本, 澳大利亚<br>LYNN MILLER, 莫纳什大学, 墨尔本, 澳大利亚<br>CHANG WEI TAN, 莫纳什大学, 墨尔本, 澳大利亚<br>GEOFFREY I. WEBB, 莫纳什大学, 墨尔本, 澳大利亚<br>GERMAIN FORESTIER, 莫纳什大学, 墨尔本, 澳大利亚 and IRIMAS, 上阿尔萨斯大学,<br>米卢斯, 法国<br>MAHSA SALEHI, 莫纳什大学, 墨尔本, 澳大利亚

#### 摘要

时间序列分类 (Time Series Classification, TSC) 和外生回归 (Extrinsic Regression) 是重要且具有挑战性的机器学习任务。深度学习已经彻底改变了自然语言处理和计算机视觉，并在其他领域（如时间序列分析）中具有巨大的潜力，在时间序列分析中，相关的特征通常必须从原始数据中抽象出来，但事先并不知道。本文综述了快速发展的深度学习在时间序列分类和外生回归领域的最新进展。我们回顾了用于这些任务的不同网络架构和训练方法，并讨论了将深度学习应用于时间序列数据时面临的挑战和机遇。我们还总结了时间序列分类和外生回归的两个关键应用：人类活动识别和卫星地球观测。

CCS 概念: $\cdot$ 计算方法 $\rightarrow$ 机器学习方法; 监督学习;
附加关键词和短语: 深度学习, 时间序列, 分类, 外生回归, 综述

## ACM 参考文献格式:

Navid Mohammadi Foumani, Lynn Miller, Chang Wei Tan, Geoffrey I. Webb, Germain Forestier, and Mahsa Salehi. 2024. Deep Learning for Time Series Classification and Extrinsic Regression: A Current Survey. ACM Comput. Surv. 56, 9, Article 217 (April 2024), 45 pages. https://doi.org/10.1145/3649448

## 1 引言

时间序列分析已被确定为 21 世纪数据挖掘领域中最具挑战性的 10 个研究问题之一 [1]。时间序列分类 (TSC) 是一项关键的时间序列分析任务 [2]。TSC 构建一个机器学习模型，用于预测由实值属性的有序集合组成的数据的类别标签。时间序列分析的许多应用

[^0]
[^0]: 本工作由澳大利亚政府研究培训计划 (Research Training Program, RTP) 奖学金资助。
    作者地址: N. Mohammadi Foumani, L. Miller, C. W. Tan, G. I. Webb, and M. Salehi, Monash University, Melbourne, Australia; e-mails: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>; G. Forestier, Monash University, Melbourne, Australia and IRIMAS, University of Haute-Alsace, Mulhouse, France; e-mail: <EMAIL>.

包括人类活动识别 [3-5]、基于电子健康记录的诊断 [6, 7] 和系统监控问题 [8]。加州大学河滨分校 (University of California, Riverside, UCR) [9] 和东英吉利大学 (University of East Anglia, UEA) [8] 基准档案中各种各样的数据集类型进一步说明了 TSC 应用的广泛性。时间序列外生回归 (TSER) [10] 是 TSC 的对应物，其输出是数值型的而不是类别型的。应该注意的是，TSER 不是一种预测方法，而是一种理解时间序列和外生变量之间关系的方法。TSER 是一个新兴领域，具有在广泛应用中使用的巨大潜力。

深度学习已经非常成功，尤其是在计算机视觉和自然语言处理领域。许多现代应用都集成了深度学习。深度学习可以自主地从原始数据中学习信息丰富的特征，从而无需手动特征工程。因此，由于深度 TSC 和 TSER 能够学习相关的潜在特征表示，因此人们对开发它们产生了浓厚的兴趣。值得注意的是，大多数 TSC 和 TSER 研究都集中在非深度学习方法上。最近的一项基准测试 [11] 表明，深度学习方法 (InceptionTime [12]) 具有竞争力，但并未优于基准档案上的最先进水平。一个原因是流行的 UCR 和 UEA 基准档案并非为深度学习模型而设计。特别是，它们相对较小，而深度学习通常在数据量较大时表现出色。深度学习还可以受益于与当前硬件（尤其是 GPU）的更高兼容性，从而实现快速高效的执行。它们卓越的可扩展性进一步允许无缝处理不断增长的数据量和计算复杂性，从而增强了它们在处理大型数据集中的多功能性。事实上，ConvTran [13] 是一种用于 TSC 的最新深度架构，在训练样本超过 1 万个时，在速度和准确性方面均优于最快的传统模型之一 ROCKET [14]。

一篇关于基于深度学习的 TSC 的极具影响力的评论文章 [15] 发表于 2019 年。然而，该研究领域发展非常迅速，之前的综述并未涵盖当前的最先进水平。例如，它不包括 InceptionTime [12]，该系统始终优于 ResNet [16]，ResNet 是之前综述中性能最佳的系统。它也不包括注意力模型，近年来注意力模型受到了极大的关注，并且在建模序列数据中的长程依赖关系方面表现出了出色的能力，并且非常适合时间序列建模 [17]。已经提出了许多注意力变体来解决时间序列建模中的特定挑战，并且已成功应用于 TSC [13, 18, 19]。此外，之前的综述不包括自监督学习，自监督学习正在成为一种新的范例 [20]。自监督学习通过设计预训练任务而不是依赖于预定义的先验知识来诱导监督，并且已经显示出非常有希望的结果，尤其是在标签数量较少的数据集中 [21-24]。

鉴于注意力机制、自监督学习和各种用于 TSC 的新网络配置的出现，对 TSC 中深度学习进行系统的全面综述将极大地惠及时间序列社区。本文旨在通过总结基于深度学习的时间序列分析（特别是 TSC 和 TSER）的最新进展来填补这一空白。在定义和简要介绍时间序列分类和外生回归任务之后，我们提出了一种基于各种方法论视角的新分类法。讨论了包括多层感知器 (Multilayer Perceptrons, MLPs)、卷积神经网络 (Convolutional Neural Networks, CNNs)、循环神经网络 (Recurrent Neural Networks, RNNs)、图神经网络 (Graph Neural Networks, GNNs) 和基于注意力的模型在内的各种架构，以及为提高性能而进行的改进。此外，还探讨了各种类型的自监督学习预训练任务，例如对比学习和自预测。我们还对有用的数据增强和迁移学习策略进行了回顾

用于时间序列数据。此外，我们还提供了 TSC 和 TSER 的两个关键应用的摘要，即人类活动识别和地球观测。


# 2 背景和定义

本节首先提供必要的定义和背景信息，以便理解用于时间序列分类（TSC）和时间序列外生回归（TSER）任务的深度神经网络（DNNs）训练主题。我们首先定义关键术语和概念，例如时间序列数据和时间序列监督学习。最后，我们提出了用于TSC和TSER任务的不同深度学习方法的分类法。

### 2.1 时间序列

时间序列数据是由时间索引的数据点序列。
定义2.1。时间序列 $X$ 是 $T$ 个测量值和时间戳对的有序集合，

$$
X=\left\{\left(x_{1}, t_{1}\right),\left(x_{2}, t_{2}\right), \ldots,\left(x_{T}, t_{T}\right)\right\}, \text { where } x_{i} \in \mathbb{R}^{D} \text { and } t_{1} \text { to } t_{T} \text { are the timestamps for some }
$$

测量值 $x_{1}$ 到 $x_{T}$。
每个 $x_{i}$ 是一个 $D$ 维的值向量，每个值对应于序列中捕获的每个特征。当 $D=1$ 时，该序列称为单变量（univariate）。当 $D>1$ 时，该序列称为多变量（multivariate）。

### 2.2 时间序列监督学习任务

本文重点关注两个时间序列学习任务：时间序列外生回归（time series extrinsic regression）和时间序列分类（time series classification）。分类和回归都是监督学习任务，旨在学习目标变量和一组时间序列之间的关系。我们考虑从一个数据集 $D=\left\{\left(X_{1}, Y_{1}\right),\left(X_{2}, Y_{2}\right), \ldots,\left(X_{N}, Y_{N}\right)\right\}$ 中学习，该数据集包含 $N$ 个时间序列，其中 $Y_{i}$ 表示每个 $X_{i}$ 的目标变量。重要的是要注意，为了便于说明，我们在讨论中假设序列的长度相同，但大多数方法可以很容易地扩展到长度不等的序列的情况。TSER和TSC的主要区别在于，TSC预测来自一组有限类别的某个时间序列的类别值，而TSER预测输入时间序列外部变量的连续值。通常，$Y_{i}$ 是TSC的one-hot编码向量，或者是TSER的数值。

在深度学习的背景下，监督学习模型是一个神经网络，它执行以下函数以将输入时间序列映射到目标变量：

$$
f_{L}\left(\theta_{L}, X\right)=f_{L-1}\left(\theta_{L-1}, f_{L-2}\left(\theta_{L-2}, \ldots, f_{1}\left(\theta_{1}, X\right)\right)\right)
$$

其中 $f_{i}$ 表示非线性函数，$\theta_{i}$ 表示第 $i$ 层的参数。对于TSC，训练神经网络模型以将时间序列数据集 $D$ 映射到一组具有 $C$ 个类别标签的类别标签 $Y$。训练后，神经网络输出一个 $C$ 值的向量，该向量估计序列 $X$ 属于每个类别的概率。这通常通过在神经网络的最后一层中使用 softmax 激活函数来实现。softmax 函数估计所有依赖类别的概率，使得它们在所有类别中始终总和为 1。交叉熵损失（cross-entropy loss）通常用于训练具有 softmax 输出或分类类型神经网络的神经网络。

另一方面，TSER训练神经网络模型以将时间序列数据集 $D$ 映射到一组数值 $Y$。回归神经网络不是输出概率，而是为时间序列输出一个数值。它通常与神经网络最后一层中的线性激活函数一起使用。但是，也可以使用任何具有单值输出的非线性函数，例如 sigmoid 或 ReLU。回归神经网络通常使用均方误差（mean square error）或平均绝对误差（mean absolute error）损失函数进行训练。但是，根据目标变量的分布和最终激活函数的选择，可以使用其他损失函数。

# 2.3 TSC 和 TSER

TSC是一个快速发展的领域，每年都有数百篇论文发表[8, 9, 15, 25, 26]。TSC中的大多数工作都不是基于深度学习的。在本调查中，我们重点关注深度学习方法，并将感兴趣的读者引导至附录A和基准论文[11, 25, 26]，以获取有关非深度学习方法的更多详细信息。TSC的大多数深度学习方法都具有映射到类别标签的实值输出。TSER [10, 27] 是一项研究较少的任务，其中预测值是数值，而不是类别。虽然本调查中涵盖的大多数架构都是为TSC设计的，但重要的是要注意，将它们中的大多数适配于TSER是很容易的。

基于深度学习的TSC方法可以分为两种主要类型：生成式（generative）和判别式（discriminative）[28]。在TSC社区中，生成式方法通常被认为是基于模型的[25]，旨在理解和建模输入序列 $X$ 和输出标签 $Y$ 的联合概率分布，表示为 $p(X, Y)$。另一方面，判别式模型侧重于建模给定输入序列 $X$ 的输出标签 $Y$ 的条件概率，表示为 $p(Y \mid X)$。

生成模型，例如堆叠去噪自编码器（Stacked Denoising Auto-encoders, SDAEs），已由Bengio等人提出[29]，以识别输入数据分布的显着结构，Hu等人[30]在为时间序列任务训练分类器之前，使用相同的模型进行预训练阶段。已经开发了一种通用神经网络编码器，用于将可变长度的时间序列转换为固定长度的表示形式[31]。此外，深度信念网络（Deep Belief Network, DBN）与迁移学习方法相结合，以无监督的方式用于建模时间序列的潜在特征[32]。回声状态网络（Echo State Network, ESN）已用于通过在训练分类器之前重建原始原始时间序列来学习适当的时间序列表示形式[33]。生成对抗网络（Generative Adversarial Networks, GANs）是一种流行的生成模型，它通过学习区分真实示例和合成示例来生成新示例。已经开发了各种用于时间序列的GAN，并在最近的一项调查中进行了回顾[34]。通常，由于需要额外的训练步骤，因此实现生成式方法更加复杂。此外，生成式方法通常不如判别式方法有效，后者直接将原始时间序列映射到类别概率分布。由于这些障碍，研究人员倾向于关注判别式方法。因此，本调查主要侧重于端到端（end-to-end）判别式方法。


### 2.4 TSC和TSER中深度学习的分类

为了对现有的TSC深度学习模型进行有组织的总结，我们提出了一个分类法，该分类法根据深度学习方法和应用领域对这些模型进行分类。该分类法如图1所示。在第3节中，我们回顾了用于TSC的各种网络架构，包括MLP、CNN、RNN、GNN和基于注意力机制的模型。我们还讨论了对这些模型进行的改进，以提高它们在时间序列任务上的性能。此外，在第4节中，我们探讨了各种类型的自监督学习预训练任务（pretext），例如对比学习和自预测。我们还在第5节和第6节中回顾了用于时间序列数据的有用的数据增强和迁移学习策略。除了方法之外，我们还在本文的第7节中总结了TSC和TSER的关键应用。这些应用包括人类活动识别和卫星地球观测，这些都是重要且具有挑战性的任务，可以从深度学习模型的使用中受益。总的来说，我们提出的分类法以及

![img-0.jpeg](images/img-0.jpeg.png)

图1. 从网络配置和应用领域的角度来看，用于TSC/TSER的深度学习的分类。
这些章节中的讨论全面概述了时间序列分析深度学习的当前技术水平，并概述了未来的研究方向。

# 3 监督模型

本节回顾了基于深度学习的TSC模型，并通过强调它们的优点和局限性来讨论它们的架构。有关深度模型架构及其对时间序列数据的适应性的更多详细信息，请参见附录B。

### 3.1 多层感知机（MLP）

最直接的神经网络架构是全连接网络，也称为MLP。层数和神经元数量被定义为MLP模型中的超参数（hyperparameters）。然而，诸如自适应MLP [35]之类的研究试图根据训练时间序列数据的性质自动确定隐藏层中的神经元数量。这使得网络能够适应训练数据的特征，并优化其在手头任务上的性能。

使用MLP处理时间序列数据的主要局限性之一是，它们不太适合捕获此类数据中的时间依赖性（temporal dependencies）。MLP是前馈网络，它以固定和预定的顺序处理输入数据，而不考虑输入值之间的时间关系。各种研究将MLP与其他特征提取器（feature extractors）(如动态时间规整（Dynamic Time Warping, DTW）)一起使用，以解决这个问题[36, 37]。DTW-NN是一个前馈神经网络，它利用DTW的弹性匹配能力来动态地将一层的输入与权重对齐，而不是使用固定和预定的输入到权重的映射。这种权重对齐用DTW代替了神经元内的标准点积。通过这种方式，DTW-NN能够解决时间序列识别的难题，例如前馈架构中的时间扭曲和可变模式长度[37]。类似地，符号聚合近似（Symbolic Aggregate Approximation, SAX）用于将时间序列转换为符号表示，并基于符号表示生成单词序列[38]。基于符号时间序列的单词稍后用作训练用于分类的双层MLP的输入。

尽管上述模型试图解决MLP模型中捕获时间依赖性的不足，但它们在捕获时间不变特征（time-invariant features）[16]方面仍然存在其他局限性。此外，MLP模型不具备以分层或多尺度方式处理输入数据的能力。时间序列数据通常在不同的尺度上表现出模式和结构，例如长期趋势和短期波动。MLP模型无法捕获这些模式，因为

表1. 用于时间序列分类和外在回归的CNN模型总结

| 模型 | 年份 | 基线架构（Baseline Architecture） | 其他特征 |
| :--: | :--: | :--: | :--: |
| 适配 |  |  |  |
| MC-DCNN [43] | 2014 | 2阶段卷积（2-Stage Conv） | 每个通道的独立卷积 |
| MC-CNN [44] | 2015 | 3阶段卷积（3-Stage Conv） | 所有通道上的1D卷积 |
| Zhao et al. [45] | 2015 | 2阶段卷积（2-Stage Conv） | 类似于MC-CNN的架构 |
| FCN [16] | 2017 | FCN | 使用GAP代替FC层 |
| ResNet [16] | 2017 | ResNet 9 | 使用3个残差块（3-residual block） |
| Res-CNN [49] | 2019 | RezNet+FCN | 使用1个残差块（1-residual block）+ FCN |
| DCNNs [51] | 2019 | 4阶段卷积（4-Stage Conv） | 使用空洞卷积（dilated convolutions） |
| Disjoint-CNN [16] | 2021 | 4阶段卷积（4-Stage Conv） | 不相交的时间和空间卷积 |
| 系列到图像 |  |  |  |
| Wang and Oates [54] | 2015 | 平铺CNN（Tiled CNN） | GAF, MT |
| Hatami et al. [55] | 2017 | 2阶段卷积（2-Stage Conv） | 递归图（Recurrence plots） |
| Karimi-Bidhendi et al. [56] | 2018 | Inception V3 | GADF |
| Zhao and Cai [57] | 2019 | ResNet18, ShuffleNet V2 | 双线性插值（Bilinear interpolation） |
| RPMCNN [61] | 2019 | VGGNet, 2阶段卷积（2-Stage Conv） | 相对位置矩阵（Relative position matrix） |
| Yang et al. [58] | 2019 | VGGNet | GASF, GADF, MTF |
| 多尺度操作 |  |  |  |
| MCNN [62] | 2016 | 2阶段卷积（2-Stage Conv） | 恒等映射，平滑，下采样 |
| t-LeNet [63] | 2016 | 2阶段卷积（2-Stage Conv） | 挤压和扩张（Squeeze and dilation） |
| MVCNN [64] | 2019 | 4阶段卷积（4 -stage Conv） | 基于Inception V1 |
| Brunel et al. [65] | 2019 | Inception V1 |  |
| InceptionTime [12] | 2019 | Inception V4 | 集成（Ensemble） |
| EEG-inception [66] | 2021 | InceptionTime |  |
| Inception-FCN [67] | 2021 | InceptionTime + FCN |  |
| KDCTime [68] | 2022 | InceptionTime | 知识蒸馏（Knowledge distillation）, 标签平滑（label smoothing） |
| LITE [69] | 2023 | InceptionTime | 多路复用，扩张和自定义滤波器 |

它们只能以单个固定长度的表示形式处理输入数据。此外，MLP在面对不规则采样的时间序列数据时可能会遇到困难，在这种情况下，观测值并非在时间上均匀记录。许多其他深度学习模型更适合处理时间序列数据，例如RNN、CNN和Transformer，它们专门用于捕获时间序列数据中的时间依赖性和模式。


# 3.2 基于CNN的模型 (CNN-based Models)

自从2012年AlexNet [39] 取得成功以来，CNN已经进行了多项改进，例如使用更深的网络，应用更小、更高效的卷积滤波器，添加池化层以降低特征图的维度，以及利用批归一化 (batch normalization) 来提高训练的稳定性 [40]。它们已被证明在许多领域都非常成功，例如计算机视觉、语音识别和自然语言处理问题 [40-42]。由于CNN架构在这些不同领域中的成功，研究人员也开始将它们用于时间序列分类 (TSC)。有关本文中回顾的CNN模型列表，请参见表1。

3.2.1 适用于TSC和TSER的改编CNN (Adapted CNNs for TSC and TSER)。本节介绍第一类，我们将其称为适用于TSC和TSER的改编CNN。此处讨论的论文大多是改编，没有任何特定的预处理或数学特征，例如将序列转换为图像或使用多尺度卷积，因此不适合其他任何类别。

用于TSC的第一个CNN是多通道深度卷积神经网络 (Multi-Channel Deep Convolutional Neural Network, MCDCNN) [43]。它通过独立地对每个输入通道应用卷积来处理多元数据。每个输入维度都经过两个带有ReLU激活的卷积阶段，然后进行最大池化 (max pooling)。来自每个维度的输出被连接起来并传递到全连接层，然后将其馈送到最终的softmax分类器以进行分类。与MC-DCNN类似，有人提出了一种三层卷积神经网络用于人类活动识别 (human activity recognition) (MC-CNN) [44]。与MC-DCNN不同，该模型同时将1D卷积应用于所有输入通道，以捕获早期阶段的时间和空间关系。Zhao等人 [45] 在最早版本的UCR时间序列数据挖掘档案 (UCR Time Series Data Mining Archive) 上使用了MC-CNN架构的两阶段版本。作者还进行了一项消融研究 (ablation study)，以评估具有不同数量的卷积滤波器和池化类型的CNN模型的性能。

全卷积网络 (Fully Convolutional Networks, FCN) [46] 和残差网络 (Residual Network, ResNet) [47] 是两种深度神经网络，通常用于图像和视频识别任务，并且已被改编用于端到端 (end-to-end) TSC [16]。FCN是CNN的一种变体，旨在处理任意大小的输入，而不是像传统CNN那样被限制为固定大小的输入。这是通过用全局平均池化 (Global Average Pooling, GAP) [46] 替换传统CNN中的全连接层来实现的。FCN被改编用于单变量TSC [16]，并且与原始模型类似，它包含三个卷积块，其中每个块包含一个卷积层，后跟批归一化和ReLU激活。每个块分别使用具有8、5和3个滤波器长度的128、256和128个滤波器。来自最后一个卷积块的输出通过GAP层进行平均，并传递到最终的softmax分类器。GAP层具有减小输入空间维度的同时保留通道信息 (channel-wise information) 的特性，这使其可以与类激活图 (class activation map, CAM) [48] 结合使用，以突出显示输入中对于预测类最重要的区域。这可以提供有关网络如何进行预测的有用见解，并有助于识别潜在的改进领域。与FCN类似，ResNet也在 [16] 中被提出用于单变量TSC。ResNet是一种深度架构，包含三个残差块，后跟一个GAP层和一个softmax分类器。它在块之间使用残差连接 (residual connections) 来减少影响深度学习模型的梯度消失效应 (vanishing gradient effect)。每个残差块的结构与FCN架构相似，包含三个卷积层，后跟批归一化和ReLU激活。每个卷积层分别使用具有8、5和3个滤波器长度的64个滤波器。ResNet被认为是85个单变量TSC数据集 [15, 25] 上最准确的深度学习TSC架构之一。此外，已经提出了ResNet和FCN的集成，以结合两个网络的优势 [49]。

除了调整网络架构外，一些研究还集中于修改卷积核，以更好地适应TSC任务。扩张卷积神经网络 (Dilated convolution neural networks, DCNNs) [50] 是一种CNN，它使用扩张卷积来增加网络的感受野 (receptive field)，而无需增加参数数量。扩张卷积在内核元素之间创建间隙并执行卷积，从而覆盖更大的输入区域。这使网络能够捕获数据中的长程依赖关系，使其非常适合TSC任务 [51]。最近，Disjoint-CNN [52] 表明，将1D卷积核分解为不相交的时间和空间分量可以提高准确性，而几乎没有额外的计算成本。应用不相交的时间卷积，然后进行空间卷积的行为类似于倒置瓶颈 (Inverted Bottleneck) [53]。与倒置瓶颈类似，时间卷积会扩展输入通道的数量，而空间卷积随后会将扩展的隐藏状态 (hidden state) 投影回原始大小，以捕获时间和空间的交互。

3.2.2 时间序列成像 (Imaging Time Series)。在TSC中，一种常见的方法是将时间序列数据转换为固定长度的表示形式，例如向量或矩阵，然后可以将其输入到深度学习模型中。但是，对于长度变化或具有复杂时间依赖关系的时间序列数据，这可能具有挑战性。解决此问题的一种方法是以图像状格式 (image-like format) 表示时间序列数据，其中每个时间步长都被视为图像中的单独通道。这使模型可以从数据中的空间关系 (spatial relationships) 中学习，而不仅仅是时间关系。在这种情况下，术语“空间”是指时间序列的单个时间步长内不同变量或特征之间的关系。


作为使用原始时间序列数据作为输入的一种替代方法，Wang 和 Oates 将单变量时间序列数据编码为不同类型的图像，然后由常规 CNN [54] 处理。这种基于图像的框架开创了时间序列深度学习方法的一个新分支，它将图像转换视为特征工程技术之一。Wang 和 Oates 提出了两种将时间序列转换为图像的方法。第一种生成格拉姆角场（Gramian Angular Field, GAF），而第二种生成马尔可夫转移场（Markov Transition Field, MTF）。GAF 在极坐标中表示时间序列数据，并使用各种操作将这些角度转换为对称矩阵，而 MTF 使用数据点从一个时间步长到另一个时间步长的转移概率来编码矩阵条目 [54]。在这两种情况下，图像生成都会增加时间序列的大小，使得图像可能变得过大而无法处理。因此，他们提出了在不损失太多信息的情况下减小图像大小的策略。之后，这两种类型的图像被组合成一个双通道图像，然后用于产生比单独使用每个图像时更好的结果。最后，应用 Tiled CNN 模型对时间序列图像进行分类。在其他研究中，已经提出了各种转换方法，包括递归图（Recurrence Plots, RPs）[55]、格拉姆角差场（Gramian Angular Difference Field, GADF）[56]、双线性插值 [57] 和格拉姆角求和场（Gramian Angular Summation Field, GASF）[58]，以将时间序列转移到输入图像，期望 2D 图像可以揭示在原始时间序列的 1D 序列中找不到的特征和模式。

Hatami 等人 [55] 提出了一种基于 RP [59] 的表示方法，用于将时间序列转换为 2D 图像，并使用 CNN 模型进行 TSC。在他们的研究中，时间序列被视为不同的循环行为，例如周期性和不规则的循环性，这是动态系统的典型现象。使用 RP 方法的主要思想是揭示一些轨迹在哪些点返回到之前的状态。最后，应用两阶段卷积和两个全连接层来对 RP 生成的图像进行分类。随后，预训练的 Inception v3 [60] 被用于将 GADF 图像映射到 2,048 维向量空间。最后阶段使用具有三个隐藏层的 MLP，然后是 softmax 激活函数 [56]。遵循相同的框架，Chen 和 Shi [61] 采用相对位置矩阵（Relative Position Matrix, RPMCNN）和 VGGNet，使用转换后的 2D 图像对时间序列数据进行分类。他们的结果表明，通过使用两个时间戳之间的相对位置将单变量时间序列数据转换为 2D 图像，可以获得有希望的性能。按照惯例，三种图像编码方法，GASF、GADF 和 MTF，被用于将 MTS 数据编码为 2D 图像 [58]。他们表明，ConvNet 的简单结构足以进行分类，因为它与 VGGNet 的复杂结构表现同样出色。

总的来说，将时间序列数据表示为 2D 图像可能很困难，因为保持数据中的时间关系和模式可能具有挑战性。这种转换也可能导致信息丢失，使得模型难以准确地对数据进行分类。Chen 和 Shi [61] 也表明，在此过程中使用的特定转换方法（如 GASF、GADF 和 MTF）并不能显着改善预测结果。
3.2.3 多尺度操作。此处讨论的论文将多尺度卷积核应用于输入序列，或在不同尺度上对输入序列应用常规卷积。多尺度 CNN（Multiscale CNNs, MCNN）[62] 和 Time LeNet (t-LeNet) [63] 被认为是第一个预处理输入序列以在多尺度序列上应用卷积而不是原始序列的模型。MCNN 和 t-LeNet 的设计都受到计算机视觉模型的启发，这意味着它们是从最初为图像识别任务开发的模型改编而来。这些模型

可能不太适合 TSC 任务，并且可能不如专门为此目的设计的模型那样表现出色。一个潜在的原因是在这些模型中使用渐进式池化层，这在计算机视觉模型中很常见，用于减少输入数据的大小并使其更易于处理。但是，这些池化层在应用于时间序列数据时可能效果不佳，并且可能会限制模型的性能。

MCNN 具有简单的架构，包括两个卷积和一个池化层，然后是一个全连接层和 softmax 层。但是，这种方法涉及大量的数据预处理。具体来说，在任何训练之前，他们使用滑动窗口来提取时间序列子序列，然后，该子序列将经历三个转换：（1）恒等映射，（2）下采样，以及（3）平滑，这导致将单变量输入时间序列转换为多变量时间序列。最后，将转换后的输出馈送到 CNN 模型以训练分类器 [62]。t-LeNet 使用两种数据增强技术：窗口切片（window slicing, WS）和窗口扭曲（window warping, WW），以防止过拟合 [63]。WS 方法与 MCNN 的数据增强相同。第二种数据增强技术 WW 采用一种扭曲技术，可以挤压或膨胀时间序列。WS 也被采用以确保提取相同长度的子序列以训练网络以处理多长度时间序列。因此，给定长度为 $L$ 的输入时间序列首先使用 WW 进行膨胀 $(\times 2)$，然后挤压 $(\times 1 / 2)$，从而产生长度为 $L, 2 L, 1 / 2 L$ 的三个时间序列，这些时间序列被馈送到 WS 以提取等长度的子序列以进行训练。最后，由于 MCNN 和 t-LeNet 都为每个提取的子序列预测一个类别，因此应用多数投票来获得完整时间序列的类别预测。

Inception 最初由 Szegedy 等人 [70] 提出，用于端到端图像分类。现在，该网络已经发展成为 Inception-v4，其中 Inception 与残差连接相结合，以进一步提高性能 [71]。受 Inception 架构的启发，设计了一种多变量卷积神经网络（multivariate convolutional neural network, MVCNN），该网络使用多尺度卷积核来寻找最佳局部结构 [64]。MVCNN 使用三个尺度的滤波器，$2 \times 2, 3 \times 3$ 和 $5 \times 5$，以提取传感器之间交互的特征。一个 1D Inception 模型被用于超新星分类，使用空间区域的光通量作为网络的输入 MTS [65]。但是，作者将他们的 Inception 架构的概念限制为该模型的第一个版本 [70]。Inception-ResNet [72] 架构包括卷积层，然后是 Inception 模块和残差块。Inception 模块用于学习数据的多个尺度和方面，从而使网络能够捕获更复杂的模式。然后，残差块用于学习网络的输入和输出之间的残差或差异，从而提高其性能。


InceptionTime [12] 探索了比之前任何用于时间序列分类(TSC)的网络更大的滤波器，以在UCR基准测试中达到最先进的性能。InceptionTime 是五个随机初始化的 Inception 网络模型的集成，每个模型由两个 Inception 模块组成。每个 Inception 模块首先使用长度和步长均为 1 的瓶颈层来降低多元时间序列的维度，同时保持相同的长度。然后，将不同长度的 1D 卷积应用于瓶颈层的输出，以提取不同大小的模式。同时，将最大池化层，然后是瓶颈层，也应用于原始时间序列，以提高模型对小扰动的鲁棒性。来自卷积和最大池化层的输出被堆叠以形成新的多元时间序列，然后将其传递到下一层。在每个 Inception 块之间使用残差连接，以减少梯度消失效应。第二个 Inception 块的输出被传递到 GAP 层，然后再馈入 softmax 分类器。

InceptionTime 的强大性能激发了许多扩展。与 InceptionTime 类似，EEG-inception [66] 使用多个 Inception 层和残差连接作为其骨干。此外，还提出了基于噪声添加的脑电图 (electroencephalogram, EEG) 信号的数据增强，从而提高了平均准确率。InceptionFCN [67] 专注于结合两种著名的深度学习技术，即 Inception 模块和全卷积网络 (Fully Convolutional Network, FCN) [67]。在 KDCTime [68] 中，引入了标签平滑 (label smoothing, LSTime) 和知识蒸馏 (knowledge distillation, KDTime) 用于 InceptionTime，在压缩推理模型时自动生成。此外，KDCTime 中的带校准的知识蒸馏 (knowledge distillation with calibration, KDC) 提供了两种校准策略：通过转换的 KDC (KDC by translating, KDCT) 和通过重新排序的 KDC (KDC by reordering, KDCR)。LITE [69] 在保持其 TSC 性能的同时，解决了 InceptionTime 的复杂性。LITE 利用深度可分离卷积 (DepthWise Separable Convolutions)，结合了多路复用、空洞卷积和自定义滤波器 [73]，以提高效率。

# 3.3 循环神经网络

循环神经网络 (Recurrent Neural Networks, RNNs) 是一种具有内部记忆的神经网络，用于处理时间序列和序列数据。在概念上类似于前馈神经网络 (feed-forward neural networks, FFNs)，RNN 的不同之处在于它们能够处理可变长度的输入并产生可变长度的输出。
3.3.1 原始循环神经网络 (Vanilla Recurrent Neural Networks, Vanilla RNNs)。[74] 中提出了用于 TSC 的 RNN。使用 RNN，输入序列已根据其动态行为进行分类。他们使用了序列到序列 (sequence-to-sequence) 架构，其中输入序列的每个子序列在第一步中进行分类。然后将 argmax 函数应用于整个输出，最后，具有最高速率的神经元指定分类结果。为了提高模型的并行性和容量，[75] 提出了一个两层 RNN。在第一层中，输入序列被分成几个独立的 RNN 以提高并行性，然后在第二层中使用第一层的输出以捕获长期依赖关系 [75]。此外，RNN 已被用于一些分层架构 [76, 77]。Hermans 和 Schrauwen 表明，更深版本的 RNN 可以在复杂的时序任务上执行分层处理，并且比浅版本更自然地捕获时间序列结构 [77]。RNN 通常使用一种称为随时间反向传播 (backpropagation through time, BPTT) 的过程进行迭代训练。当按时间展开时，RNN 看起来像具有共享参数的非常深的网络。由于 RNN 中更深的神经层和跨不同 RNN 单元共享权重，因此梯度在每个时间步进行求和以训练模型。因此，由于链式法则，梯度会经历连续的矩阵乘法，并且要么呈指数收缩并具有称为梯度消失的小值，要么膨胀到非常大的值，称为梯度爆炸 [78]。这些问题促使了用于深度架构的二阶方法的发展，即长短期记忆网络 (long short-term memory, LSTM) [79] 和门控循环单元 (Gated Recurrent Unit, GRU) [80]。
3.3.2 长短期记忆网络 (Long Short-Term Memory, LSTM)。LSTM 通过将具有门控控制的记忆单元集成到其状态动态中，解决了原始 RNN 中常见的梯度消失/爆炸问题 [79]。由于其设计特性，LSTM 适用于涉及序列数据的问题，例如语言翻译 [81]、视频表示学习 [82] 和图像字幕生成 [83]。TSC 问题也不例外，并且主要采用与语言翻译 [81] 类似的模型。带注意力的序列到序列 (Sequence-to-Sequence with Attention, S2SwA) [84] 在序列到序列的方式中结合了两个 LSTM，一个编码器和一个解码器，用于 TSC。在该模型中，编码器 LSTM 接受任意长度的输入时间序列，并从原始数据中提取信息，解码器 LSTM 基于该信息构建固定长度的序列，这些序列可以被视为自动提取的用于分类的特征。
3.3.3 门控循环单元 (Gated Recurrent Unit, GRU)。GRU 是 RNN 的另一种广泛使用的变体，它在控制信息流和跨多个时间步记忆上下文的能力方面与 LSTM 相似 [80]。与 S2SwA [84] 类似，已经定义了基于 GRU 的序列自编码器 (sequence auto-encoder, SAE) 来处理 TSC 问题 [85]。通过使用 GRU 作为编码器和解码器处理各种输入长度来产生固定大小的输出。通过对大量未标记数据进行参数预训练，也提高了模型的准确性。
3.3.4 混合模型。CNN 和 RNN 通常结合用于 TSC，因为它们具有互补的优势。如前所述，CNN 非常适合从数据中的空间关系中学习，例如时间序列中不同时间步的通道之间的模式和相关性。这使他们能够从时间序列数据中学习有用的特征，这些特征可以帮助提高分类性能。另一方面，RNN 非常适合从数据中的时间依赖性中学习，例如时间序列的过去值，这些值可以帮助预测其未来值。这使他们能够捕获时间序列数据的动态特性并做出更准确的预测。结合 CNN 和 RNN 的优势，可以从时间序列数据中学习空间和时间特征，从而提高模型对 TSC 的性能。此外，这两个模型可以一起训练，从而使它们可以相互学习并提高模型的整体性能。

后来提出了各种扩展，例如 MLSTM-FCN [86]、TapNet [87] 和 SMATE [88]，以处理时间序列数据。MLSTM-FCN 将单变量 LSTM-FCN 模型 [89] 扩展到多变量的情况。与 LSTM-FCN 类似，多变量版本包括 LSTM 块和全卷积块，用于从输入序列中提取特征。还将一个 squeeze and excite 块添加到 FCN 块中，并且可以在先前层的输出特征图上执行一种形式的自注意力 [86]。另外两个关于多变量 TSC 的提议是时间序列注意力原型网络 (Time series attentional prototype Network, TapNet) 和半监督时空 (Semi-Supervised Spatio-Temporal, SMATE) [87, 88]。这些方法结合并试图利用传统基于距离的方法和深度学习方法的相对优势。


MLSTM-FCN, TapNet 和 SMATE 被设计成双网络架构。输入分别被送入 CNN 和 RNN 模型，它们的输出在全连接层之前被集中起来用于最终任务。然而，由于最终的分类结果是通过连接两个分支的输出来生成的，因此一个分支无法充分利用另一个分支在特征提取期间的隐藏状态。 这激发了不同类型的架构，这些架构试图对 CNN 和 RNN 模型进行逐层集成。这激发了不同的架构，例如 GCRNN [90] 和 CNN-LSTM [91]，它们旨在以逐层方式集成 CNN 和 RNN。

虽然 RNN 通常用于时间序列预测，但只有少数研究将其应用于 TSC（时间序列分类 (Time Series Classification)），这主要是由于四个原因：（1）由于在长时间序列上进行训练，RNN 通常难以解决梯度消失和梯度爆炸问题 [92]；（2）RNN 被认为难以训练和并行化，因此研究人员不太可能使用它们，因为它们在计算上很昂贵 [78]；（3）循环架构主要被设计为从先前的数据中学习，以对未来进行预测 [28]；（4）RNN 模型可能无法有效地捕获和利用长序列中的长程依赖关系 [84]。

# 3.4 基于注意力机制的模型

尽管 CNN 模型在捕获局部时间/空间相关性方面表现出色，但这些模型无法有效地捕获和利用长程依赖关系。此外，它们只考虑数据点的局部顺序，而不是所有数据点的整体顺序。因此，最近的许多研究都将 RNN（如 LSTM）与 CNN 嵌入在一起，以捕获这些信息 [86, 87, 89]。基于 RNN 的模型的缺点是它们在计算上很昂贵，并且它们捕获长程依赖关系的能力有限 [18, 93]。另一方面，注意力模型可以捕获长程依赖关系，并且它们更广阔的感受野提供了更多的上下文信息，这可以提高模型的学习能力。

表 2. 用于时间序列分类和外在回归的基于注意力机制的模型总结

| 模型 | 年份 | 嵌入 (Embedding) | 注意力机制 (Attention) |
| :--: | :--: | :--: | :--: |
| MuVAN [99] | 2018 | Bi-GRU | 自注意力机制 (Self-attention) |
| ChannelAtt [102] | 2018 | RNN | 自注意力机制 (Self-attention) |
| GeoMAN [103] | 2018 | LSTM | 自注意力机制 (Self-attention) |
| Multi-Stage-Att [104] | 2020 | LSTM | 自注意力机制 (Self-attention) |
| CT_CAM [105] | 2020 | FCN + Bi-GRU | 自注意力机制 (Self-attention) |
| CA-SFCN [18] | 2020 | FCN | 自注意力机制 (Self-attention) |
| RTFN [106] | 2021 | CNN + LSTM | 自注意力机制 (Self-attention) |
| LAXCAT [100] | 2021 | CNN | 自注意力机制 (Self-attention) |
| MACNN [101] | 2021 | 多尺度 CNN (Multi-scale CNN) | Squeeze-and-excitation |
| WHEN [107] | 2023 | CNN + BiLSTM | 自注意力机制 (Self-attention) |
| SAnD [112] | 2018 | 线性嵌入 (Linear Embedding) | 多头 (Multi-head) |
| T2 [115] | 2021 | 高斯过程回归 + 1D 卷积 (Gaussian Process Regression + 1D Conv) | 多头 (Multi-head) |
| GTN [116] | 2021 | 线性嵌入 (Linear Embedding) | 多头 (Multi-head) |
| TRANS_tf [113] | 2021 | 时频特征 (Time-Frequency Features) | 多头 (Multi-head) |
| FMLA [117] | 2022 | 可变形 CNN (Deformable CNN) | 多头 (Multi-head) |
| AutoTransformer [118] | 2022 | 多尺度 CNN + NAS (Multi-scale CNN + NAS) | 多头 (Multi-head) |
| ConvTran [13] | 2023 | Disjoint-CNN | 多头 (Multi-head) |

注意力机制旨在通过关注重要特征并抑制不必要的特征来增强网络的表示能力。毫不奇怪，随着注意力模型在自然语言处理中的成功 [93, 94]，许多先前的研究试图将注意力模型的力量引入到各个领域，例如计算机视觉 [95] 和时间序列分析 [18, 19, 96-98]。表 2 列出了本文中回顾的基于注意力机制的模型。
3.4.1 自注意力机制 (Self-attention)。自注意力机制已被证明在各种自然语言处理任务中有效，因为它能够捕获文本中的长期依赖关系 [93]。最近，它也被证明对 TSC 任务有效 [18, 99-101]。正如我们提到的，自注意力模块被嵌入到编码器-解码器模型中以提高模型性能。然而，只有编码器和自注意力模块被用于 TSC。早期的 TSC 模型遵循自然语言处理模型的相同骨干，并使用基于循环的模型，例如 RNN [102]、GRU [99] 和 LSTM [103, 104] 来编码输入序列。例如，多视图注意力网络 (Multi-View Attention Network, MuVAN) 将双向 GRU 独立地应用于每个输入维度作为编码器，然后将所有表示馈送到自注意力块 [99]。

由于 CNN 模型的出色性能，许多研究试图在使用注意力机制之前使用 CNN 对时间序列进行编码 [18, 100, 105, 106]。交叉注意力稳定全卷积神经网络 (CrossAttention Stabilized Fully Convolutional Neural Network, CA-SFCN) [18] 和局部感知可解释卷积注意力网络 (LocalityAware eXplainable Convolutional ATtention network, LAXCAT) [100] 应用自注意力机制来利用 MTSC 任务的长期依赖关系。CA-SFCN 结合了 FCN 和两种类型的自注意力机制，即时间注意力 (Temporal Attention, TA) 和变量注意力 (Variable Attention, VA)，它们相互作用以捕获长程依赖关系和变量的交互。LAXCAT 还使用时间和变量注意力来识别信息变量以及它们具有用于分类的信息模式的时间间隔。小波 DTW 混合注意力网络 (WaveletDTW Hybrid attEntion Networks, WHENs) [107] 将两种注意力机制（即小波注意力和 DTW 注意力）集成到 BiLSTM 中，以提高模型性能。在小波注意力中，他们利用小波来计算注意力分数，专门针对非平稳时间序列中动态频率分量的分析。同时，DTW 注意力采用 DTW 距离来计算注意力分数，解决了多个时间序列中时间扭曲的挑战。

已经开发了几种自注意力模型来提高网络性能 [108, 109]，包括 Squeeze-and-Excitation (SE) [110]，它专注于通道注意力，通常用于对时间序列数据进行分类 [86, 101, 111]。SE 块允许整个网络使用全局信息来有选择地关注信息丰富的特征图并抑制不太重要的特征图 [110]。更重要的是，SE 块可以提高早期层中共享的较低级别表示的质量，并且在响应后期层中的不同输入时变得越来越专业化。每个特征图的权重在网络的每一层自动学习，SE 块可以提高整个网络中的特征区分能力。多尺度注意力卷积神经网络 (Multi-scale Attention Convolutional Neural Network, MACNN) [101] 应用不同的内核大小卷积，通过生成不同尺度的特征图来捕获沿时间轴的不同尺度的信息。然后，使用 SE 块通过自动学习每个特征图的重要性来增强有用的特征图并抑制不太有用的特征图。


3.4.2 Transformers（变换器）. 多头注意力机制的卓越性能促使人们尝试将多头注意力机制应用于时间序列分类（TSC）领域。用于分类的Transformers通常采用一个简单的编码器结构，该结构由注意力层和前馈层组成。Simply Attend and Diagnose (SAnD) [112] 架构首次采用类似于 vanilla transformer [93] 的多头注意力机制来对临床时间序列进行分类。该模型使用位置编码和密集插值嵌入技术，将时间顺序纳入表征学习中。在另一项对振动信号进行分类的研究 [113] 中，诸如频率系数（Frequency Coefficients）和短时傅里叶变换（Short Time Fourier Transformation, STFT）频谱等时频特征被用作Transformers的输入嵌入。一个基于多头注意力的模型被应用于原始光学卫星TSC，使用高斯过程插值（Gaussian Process Interpolation）[114]嵌入，并且性能优于卷积神经网络（CNNs）和循环神经网络（RNNs）[115]。

门控Transformer网络（Gated Transformer Networks, GTNs）[116] 使用双塔多头注意力机制来捕捉输入序列中的判别信息。此外，它们使用一个名为门控（gating）的可学习矩阵来合并两个塔的输出。为了增强Transformers对TSC的局部感知能力，灵活多头线性注意力（flexible multi-head linear attention, FMLA）[117]集成了可变形卷积块和在线知识蒸馏，以及一个随机掩码来减少噪声。对于每个TSC数据集，AutoTransformer 在将输出馈送到多头注意力块之前，使用神经架构搜索（neural architecture search, NAS）算法来搜索合适的网络架构。ConvTran [13] 目前是多变量TSC领域的最先进技术（state of the art）。他们对现有的TSC中绝对和相对位置编码方法进行了回顾。基于当前时间序列位置编码的局限性，他们分别针对绝对和相对位置引入了两种新的位置编码，分别命名为 tAPE 和 eRPE。将这些提出的位置编码集成到Transformer块中，并将它们与卷积层相结合，他们提出了一种用于多变量时间序列分类的新型深度学习框架——ConvTran。

# 3.5 图神经网络

虽然卷积神经网络（CNNs）和循环神经网络（RNNs）在欧几里得数据上表现良好，但许多时间序列问题的数据更自然地表示为图 [119]。例如，在传感器网络中，传感器的间距可能是不规则的，而不是传感器形成规则的网格。与使用欧几里得空间相比，由此网络收集的数据的图表示可以更准确地对这种不规则布局进行建模。然而，使用标准的深度学习算法从图结构中学习是具有挑战性的 [120]。例如，节点可能具有不同数量的相邻节点，从而难以应用卷积运算。

图神经网络（Graph Neural Networks, GNNs）[121] 是将深度学习技术应用于图领域的方法。早期使用图神经网络进行时间序列分析的大部分研究都集中在预测任务上 [119]。

表3. 用于时间序列分类和外部回归的图神经网络模型总结

| 模型 | 年份 | GNN类型 | 其他组件 |
| :--: | :--: | :--: | :--: |
| TGCN [126] | 2019 | 图卷积网络（Graph convolutional network） | 1D-CNN |
| DGCNN [127] | 2020 | 图卷积网络（Graph convolutional network） | 1x1 CNN |
| GraphSleepNet [128] | 2020 | 图卷积网络（Graph convolutional network） | 时间注意力（Temporal attention） |
| T-GCN [129] | 2021 | 图卷积网络（Graph convolutional network） | GRU |
| MRF-GCN [130] | 2021 | 图卷积网络（Graph convolutional network） | 快速傅里叶变换（Fast Fourier transforms, FFT） |
| Nhu et al. [131] | 2021 | 图卷积网络（Graph convolutional network） | 1D-CNN |
| DCRNN [132] | 2021 | 图卷积网络（Graph convolutional network） | GRU |
| Time2Graph+ [125] | 2021 | 图注意力（Graph attention） | Shapelet变换 |
| RAINDROP [133] | 2021 | 图引导网络（Graph guided network） | 时间注意力（Temporal attention） |
| STEGON [134] | 2021 | 图注意力（Graph attention） | 1D-CNN |
| Azevedo et al. [135] | 2022 | 带有池化的图网络块（Graph network block with pooling） | 1D-CNN |
| MTPool [136] | 2022 | 变分图池化（Variational graph pooling） | 1D-CNN |
| SimTSC [137] | 2022 | 图卷积网络（Graph convolutional network） | DTW, ResNet |
| Tulczyjew et al. [138] | 2022 | 图卷积网络（Graph convolutional network） | 自适应池化（Adaptive pooling） |
| C-DGAM [139] | 2023 | 图注意力（Graph attention） | 带有注意力的1D-CNN |
| Dufourg et al. [140] | 2023 | 时空图（Spatio-temporal graph） | 简单线性迭代聚类（Simple linear iterative clustering） |
| TISER-GCN [124] | 2023 | 图卷积网络（Graph convolutional network） | 1D-CNN |
| TodyNet [123] | 2023 | 动态图神经网络（Dynamic graph neural network） | 1D-CNN |
| LB-SimTSC [122] | 2023 | 图卷积网络（Graph convolutional network） | 下界DTW（Lower-bound DTW）, ResNet |

然而，最近的工作考虑将图神经网络（GNNs）用于时间序列分类（TSC）[122, 123] 和时间序列外部回归（TSER）[124] 任务。本文回顾的图神经网络模型列表如表3所示。Time2Graph+ [125] 将每个时间序列转换为一个shapelet图。Shapelet从时间序列中提取并形成图节点。图边缘根据两个shapelet之间的转移概率进行加权。一旦输入图被构建，图注意力网络被用于创建时间序列的表示，该表示被馈送到分类器中。SimTSC [137] 构建一个成对相似性图，其中每个时间序列形成一个节点，并且边缘权重基于动态时间规整（Dynamic Time Warping, DTW）距离度量来计算。节点属性是使用特征向量编码器生成的。图神经网络（GNN）操作用于增强基于相邻时间序列之间相似性的节点特征。这些表示然后用于最终的分类步骤，该步骤为每个节点生成一个分类。LB-SimTSC [122] 使用 LB-Keogh 下界方法 [141] 替换了昂贵的动态时间规整（DTW）计算。

时空图神经网络使用两个协同工作的模块对空间（或变量间）和时间依赖性进行建模。空间模块通过在图神经网络（GNN）（图卷积网络（GCNs）[142]）上应用图卷积来对时间序列之间的依赖性进行建模。时间模块使用循环神经网络（RNN）[129, 132]、一维卷积神经网络（1D-CNN）[134, 135]、注意力机制（Attention）[133, 139] 或它们的组合 [119] 来对时间序列中的依赖性进行建模。从图层提取的特征然后被馈送到分类或回归层，以进行单个预测 [132, 133, 135, 139] 或每个节点的预测 [129, 134]。时空图卷积网络（GCNs）通常用于分析传感器阵列，其中图结构对传感器的物理布局进行建模。


一个常见的例子是脑电图（EEG）数据，其中脑电图电极的位置被表示为一个图，用于分析脑电图信号。其中一些应用包括癫痫检测[131]，癫痫发作检测[126, 132]，情绪识别[127]和睡眠分类[128]。除了脑电图，图卷积网络（GCNs）也已应用于工程应用，例如机器故障诊断[130]，边坡变形预测[129]和地震活动预测[124]。 MTPool [136]使用时空图卷积网络（spatiotemporal GCN）进行多元时间序列分类。在这项研究中，时间序列中的每个通道都由图中的一个节点表示，并且图的边对通道之间的相关性进行建模。图卷积网络与时间卷积和分层图池化技术相结合。时空图神经网络（Spatiotemporal GNNs）也已用于基于对象的图像分析[134]和图像时间序列的语义分割[138]。但是，这些假设标签和空间关系随时间推移是静态的。在许多情况下，这些都可能发生变化。时空图（STGs）包括时间边和空间边，可以对这些动态关系进行建模[140]。在时空图中，每个节点代表一个时间戳的对象。空间边将对象连接到相邻对象，如果两个对象在连续图像中具有共同的像素，则时间边将它们连接起来。

# 4 自监督模型（SELF-SUPERVISED MODELS）

为大型时间序列数据集获取带标签的数据会带来巨大的成本和挑战。与在稀疏标记的数据集、标签有限的小型数据集或没有监督的数据集上训练的模型相比，在大型标记时间序列数据集上训练的机器学习模型通常表现出卓越的性能，从而导致各种时间序列机器学习任务[23, 143]的次优性能。因此，研究人员和从业人员越来越多地将重点转向时间序列的自监督表示学习，而不是依赖于大型数据集的高质量注释。

自监督表示学习是机器学习的一个子领域，专注于从没有明确监督的数据中学习表示[24]。与依赖于标记数据的监督学习相反，自监督学习方法利用数据的内在结构以无监督的方式学习有价值的表示。然后，可以将学习到的表示用于各种下游任务，包括分类、异常检测和预测。本调查特别强调分类作为下游任务。我们根据前置任务（pretext）将时间序列分类（TSC）的自监督学习方法分为三类。表4显示了本文中回顾的自监督模型列表。

### 4.1 对比学习（Contrastive Learning）

对比学习涉及模型学习区分正样本和负样本时间序列示例。时间对比学习（Time-Contrastive Learning, TCL）[144]、可扩展表示学习（Scalable Representation Learning, SRL 或 T-Loss）[145]和时间邻域编码（Temporal Neighborhood Coding, TNC）[146]应用基于子序列的采样，并假设距离较远的片段是负对，而相邻片段是正对。TNC利用信号生成过程的局部平滑性，在时间上定义具有平稳特性的邻域，以进一步提高对比损失函数（contrastive loss function）的采样质量。TS2Vec [23]使用对比学习来分层获得每个时间戳的鲁棒上下文表示。它涉及从输入中随机采样两个重叠的子序列，并鼓励公共片段上上下文表示的一致性。编码器使用时间对比损失（temporal contrastive loss）和实例级对比损失（instance-wise contrastive loss）进行优化。

除了基于子序列的方法外，其他模型还采用基于实例的采样[21, 143, 147-150]，单独处理每个样本以生成正样本和负样本

表4. 时间序列分类和外在回归的自监督模型总结

| 模型 | 年份 | 编码器骨干网络（Encoder Backbones） |  |
| :--: | :--: | :--: | :--: |
| 对比学习（Contrastive Learning） |  |  | 其他特征（Other Features） |
| TCL [144] | 2016 | MLP | 基于序列的对比（Sequence-based contrast） |
| T-Loss/SRL [145] | 2019 | 因果卷积神经网络（Causal CNN） | 基于序列的对比（Sequence-based contrast） |
| TNC [146] | 2021 | 双向循环神经网络（Bidirectional RNN） | 基于序列的对比（Sequence-based contrast） |
| TS-TCC [21] | 2021 | CNN + Transformers | 基于实例/序列的对比（Instance/sequence-based contrast） |
| MCL [147] | 2021 | 全卷积网络（FCN） | 基于实例的对比（Instance-based contrast） |
| TimeCLR [148] | 2021 | InceptionTime | 基于实例的对比（Instance-based contrast） |
| TS2Vec [23] | 2021 | 扩张卷积神经网络（Dilated CNN） | 基于序列的对比（Sequence-based contrast） |
| BTSF [143] | 2022 | 因果卷积神经网络（Causal CNN） | 基于实例的对比（Instance-based contrast） |
| TF-C [149] | 2022 | ResNets | 基于实例的对比（Instance-based contrast） |
| MHCCL [150] | 2023 | ResNet | 基于实例的对比（Instance-based contrast） |
| 自预测（Self-Prediction） |  |  |  |
| BENDR [98] | 2021 | CNN + Transformers | 序列掩码（Sequence masking） |
| Voice2Series [22] | 2021 | CNN+Transformers | 二元掩码（Binary masking） |
| TST [19] | 2021 | Transformers | 二元掩码（Binary masking） |
| TARNet [151] | 2022 | Transformers | 二元掩码（Binary masking） |
| TimeMAE [152] | 2023 | CNN + Transformers | 序列掩码（Sequence masking） |
| CRT [153] | 2023 | Transformers | 序列掩码（Sequence masking） |
| 其他前置任务（Other Pretext tasks） |  |  |  |
| PHIT [154] | 2023 | H-InceptionTime |  |
| Series2Vec [24] | 2023 | 不相交卷积神经网络（Disjoint CNN） | 基于相似性的表示学习（Similarity-based representation learning） |

样本用于对比损失。时间序列时间和上下文对比（Time-series Temporal and Contextual Contrasting, TSTCC）[21]使用弱增强和强增强将输入序列转换为两个视图，然后使用时间对比模块（temporal contrasting module）来学习鲁棒的时间表示。然后，对比上下文模块（contrasting contextual module）建立在时间对比模块的上下文之上，旨在最大化同一样本的上下文之间的相似性，同时最小化不同样本的上下文之间的相似性。类似地，TimeCLR [148]引入了DTW数据增强，以增强对相位偏移和幅度变化现象的鲁棒性。双线性时间谱融合（Bilinear TemporalSpectral Fusion, BTSF）[143]使用简单的dropout作为增强方法，旨在将频谱信息融入到特征表示中。同样，时频一致性（Time-Frequency Consistency, TF-C）[149]是一种自监督学习方法，它利用频域来实现更好的表示。它提出，从同一时间序列样本中学习到的基于时间和基于频率的表示，在时频空间中应该比不同时间序列样本的表示更相似。


# 4.2 自我预测

基于自我预测的自监督模型的主要目标是重建输入或输入数据的表示。研究已经探索了使用基于Transformer的自监督学习方法进行时间序列分类(TSC) [19, 22, 98, 151-153]，这得益于像BERT [94]这样的模型的成功。BErtinspired Neural Data Representations (BENDR) [98] 使用Transformer结构来建模脑电图(EEG)序列，并表明它可以有效地处理使用不同硬件记录的大量脑电图数据。另一项研究，Voice-to-Series with Transformer-based Attention (V2Sa) [22]，利用大规模预训练的语音处理模型进行TSC。

基于Transformer的框架 (TST) [19] 和 TARNet [151] 将原始Transformer适配到多元时间序列领域，并使用基于自我预测的自监督预训练方法，采用掩码数据。这些研究表明了使用基于Transformer的自监督学习方法进行TSC的潜力。

# 4.3 其他预训练任务 (Pretext Tasks)

虽然自监督学习中的许多预训练任务通常是对比的或自我预测的，但特定的任务是为时间序列数据量身定制的。在基于图像的自监督学习中，创建图像的合成变换（增强），并且模型学习将图像及其变换与训练数据中的其他图像进行对比，这对于对象解释非常有效。然而，关于有意义的自监督学习任务的定义，时间序列分析从根本上不同于视觉或自然语言处理。

在这一见解的指导下，Foumani等人 [24] 介绍了Series2Vec，一种新颖的自监督表示学习方法。与时间序列中其他对比自监督方法不同，这些方法存在正样本变体与锚样本的相似度低于负样本集中序列的风险，Series2Vec经过训练，可以通过自监督任务来预测两个序列在时域和频域中的相似性。Series2Vec主要依赖于无监督相似性步骤的一致性，而不是相似性测量的内在质量，而不需要手工制作的数据增强。Pre-trained HInceptionTime (PHIT) [154] 使用一种新颖的预训练任务进行预训练，该任务旨在识别每个时间序列样本的原始数据集。目标是生成可以应用于各种数据集的灵活卷积滤波器。此外，PHIT证明了其在小型数据集中减轻过拟合的能力。

## 5 数据增强 (DATA AUGMENTATION)

在深度学习领域，数据增强的概念已经成为提高性能的重要工具，尤其是在训练数据可用性有限的情况下 [155]。数据增强最初在计算机视觉中提出，涉及对图像的各种变换，例如裁剪、旋转、翻转以及应用模糊和锐化等滤波器。这些变换旨在在训练数据中引入各种场景，从而有助于开发更鲁棒和更具泛化性的模型。然而，将这些基于图像的增强技术直接应用于时间序列数据通常被证明是不充分或不适当的。像旋转这样的操作可能会破坏时间序列数据的内在时间结构。

过拟合的挑战在用于TSC的深度学习模型领域尤为突出。这些模型的特点是具有大量的可训练参数，这可能导致模型在训练数据上表现良好，但无法推广到未见过的数据。在这种情况下，数据增强可能是一种有价值的策略。它提供了一种替代方案，可以替代收集额外的真实世界数据的昂贵且有时不切实际的方法。通过从现有数据集生成合成样本，我们可以有效地增加训练数据的大小和多样性。以下详细介绍了用于生成合成时间序列以进行数据增强的不同研究方法。

随机变换 (Random Transformations)。已经开发了几种用于幅度域的增强方法。Um等人 [156] 探索的抖动 (Jittering) 涉及向时间序列添加随机噪声。另一种方法，翻转 (flipping) [157]，反转时间序列值。缩放 (Scaling) 是一种将时间序列乘以来自高斯分布的因子的技术。幅度扭曲 (Magnitude warping) 与缩放相似，沿着平滑变化的曲线扭曲序列。对于时域变换，置换算法起着重要作用。例如，切片变换 (slicing transformation) 涉及从序列中删除子序列。还有各种扭曲方法，如随机扭曲 (Random Warping) [158]、时间扭曲 (Time Warping) [156]、时间拉伸 (Time Stretching) [159] 和时间扰动 (Time Perturbation) [160]，每种方法都对时间序列引入不同形式的扭曲。最后，在频域中，变换通常使用傅里叶变换。例如，Gao等人 [161] 在傅里叶变换后对幅度和相位谱都引入了扰动。

窗口方法 (Window methods)。窗口方法中的主要方法是通过组合来自同一类别的各种序列的片段来创建新的时间序列。这种技术有效地丰富了数据池，使其包含各种样本。Cui等人 [162] 引入的窗口切片 (Window slicing) 涉及将时间序列分成更小的片段，每个片段保留原始序列的类别标签。然后，这些片段用于训练分类器，从而提供数据的详细视图。在分类期间，每个片段都会被单独评估，并且通过切片之间的投票系统来达成关于最终标签的集体决定。另一种技术是基于DTW算法的窗口扭曲 (window warping)。这种方法沿着时间轴调整时间序列的片段，可以拉伸或压缩它们。这引入了数据的时间维度的可变性。Le Guennec等人 [163] 提供了窗口切片和窗口扭曲的应用示例，展示了它们在增强时间序列数据集的多样性和代表性方面的有效性。

平均方法 (Averaging methods)。时间序列数据增强中的平均方法结合了多个序列以形成新的统一序列。这个过程比看起来更困难，因为它需要仔细考虑数据的时间和幅度方面的噪声和失真等因素。在这种情况下，Forestier等人 [164] 引入的加权DTW重心平均 (weighted DTW Barycenter Averaging, wDBA) 提供了一种平均方法，通过以考虑其时间动态的方式对齐时间序列。Ismail Fawaz等人 [165] 的研究说明了wDBA的实际应用，其中它与ResNet分类器结合使用，证明了它的有效性。此外，Terefe等人 [166] 进行的研究使用自编码器来平均一组时间序列。这种方法代表了时间序列数据增强中更先进的方法，利用自编码器学习和重建数据的能力来生成时间序列的平均表示。


数据增强方法的选择。选择合适的数据增强技术至关重要，并且必须根据数据集的具体特征和所使用的神经网络的架构进行调整。Iwana 和 Uchida [167]、Pialla 等人 [168] 以及 Gao 等人 [169] 进行的研究突出了这项任务的复杂性。这些研究表明，增强技术的有效性在不同的数据集和神经网络架构之间可能存在显著差异。因此，在一种情况下证明有效的方法不一定在另一种情况下产生类似的结果。为此，TSC 领域的从业者必须参与一个谨慎且知情的方法选择和调整过程。虽然现有的数据增强技术为解决有限数据和过拟合的挑战提供了一个全面的工具包，但它们的成功应用在很大程度上取决于对方法本身和手头任务的特定需求的细致理解。

# 6 迁移学习

迁移学习最初在计算机视觉领域流行起来，现在在 TSC 领域也越来越重要。在计算机视觉中，这种方法涉及使用预训练的网络（通常在像 ImageNet [170] 这样的大型数据集上），作为起点，而不是从随机网络权重开始。这种方法也与基础模型（foundation models）或基模型（base models）的概念相关，这些模型是在大量数据上训练的大规模机器学习模型，通常使用自监督或半监督学习。这些模型适用于各种任务，

展示了它们的多功能性。迁移学习的原理也与领域自适应（domain adaptation）密切相关，领域自适应侧重于将源数据分布上训练的模型应用于不同的但相关的目标数据分布。这种方法对于利用预训练模型进行各种应用至关重要，尤其是在数据稀缺或特定于某些领域的情况下。

在 TSC 的背景下，Ismail Fawaz 等人 [171] 的工作做出了贡献，他们使用 UCR 档案进行了一项研究。他们的大量实验表明，迁移学习可能会导致积极或消极的结果，具体取决于选择用于迁移的数据集。这一发现强调了源数据集和目标数据集之间关系在迁移学习效果中的重要性。Ismail Fawaz 等人 [171] 还引入了一种通过使用 DTW 来衡量数据集之间的相似性来预测 TSC 中迁移学习成功率的方法。该指标可作为为给定目标数据集选择最合适的源数据集的指南，从而在大多数情况下提高准确性。

其他研究人员也探索了 TSC 中的迁移学习。Spiegel [172] 在使用相异空间来丰富 TSC 中的特征表示方面的工作，为使用非常规数据源开创了先例。这种使用多样化数据类型来增强学习的方法，与 Li 等人 [173] 的方法有相似之处，后者利用来自各个领域的传感器模态标签来训练深度网络，强调了通用数据在迁移学习中的重要性。在数据多样性概念的基础上，Rotem 等人 [174] 通过生成用于迁移学习的合成单变量时间序列数据集，进一步突破了界限。这个用于回归任务的合成数据集，强调了人工数据在克服现实世界数据集局限性方面的潜力。此外，Senanayaka 等人 [175] 引入了基于相似性的多源迁移学习 (SiMuS-TL) 方法。通过建立一个“混合领域（mixed domain）”来模拟各种来源之间的相似性，Senanayaka 等人证明了在迁移学习中仔细选择和相关的数据源的有效性。最后，Kashiparekh 等人 [176] 的 ConvTimeNet (CTN) 专注于预训练网络在不同时间尺度上的适应性。

虽然所探索的研究共同促进了我们对 TSC 中迁移学习的理解，但该领域仍有待进一步研究。一个关键的挑战在于确定最适合迁移的源模型，由于与计算机视觉领域相比，时间序列分析中大型、精心策划和注释的数据集相对稀缺，因此这项任务变得复杂。这限制了迁移学习在 TSC 中的效用，因为广泛而多样的数据集的可用性对于开发稳健且可推广的模型至关重要。此外，开发足够通用的滤波器以在广泛的应用中有效的问题仍未解决。这方面对于迁移学习的成功至关重要，因为预训练模型对新任务的适用性取决于其学习到的特征的通用性。此外，在迁移过程中是冻结网络的某些层还是微调整个网络的策略是另一个值得深入探索的领域。

# 7 应用：最新进展与挑战

TSC 和 TSER 技术已被用于分析和建模各种应用中随时间变化的数据。这些应用包括人类活动识别、地球观测、医疗诊断（包括脑电图 (EEG) [177] 和心电图 (ECG) [178] 监测）、空气质量和污染预测 [179, 180]、结构和机器健康监测 [181, 182]、工业物联网 (IIOT) [183]、能源消耗和异常检测 [184] 以及生物声学 [185]。

由于使用 TSC 和 TSER 的应用范围广泛，因此无法在单个综述中详细介绍所有这些应用。因此，在本综述中，我们仅关注两个应用：人类活动识别和卫星地球观测。（上面提到的其他应用的最新综述参考文献已提供。）这是两个重要但截然不同的领域，选择它们是为了让读者了解时间序列在深度学习中的多样性。以下各节概述了 TSC 和 TSER 的使用、最新进展以及这两个应用中的挑战。


# 7.1 人类活动识别

人类活动识别 (HAR) 是通过分析传感器或其他仪器收集的数据来识别或监控人类活动 [186]。可穿戴技术和物联网的最新发展不仅导致了大量活动数据的收集 [187]，而且还使得利用这些数据来提高人类生活安全和质量的应用程序易于部署 [5, 186]。因此，HAR 是一个重要的研究领域，其应用包括医疗保健、健身监测、智能家居 [188] 和辅助生活 [189]。

用于收集 HAR 数据的设备可以分为基于视觉的或基于传感器的 [4, 5]。基于传感器的设备可以进一步分为对象传感器（例如，嵌入到对象中的 RFID）、环境传感器（固定位置的运动传感器、WiFi 或蓝牙设备）和可穿戴传感器 [4]，包括智能手机 [3]。然而，大多数 HAR 研究使用来自可穿戴传感器或视觉设备的数据 [186]。此外，来自视觉设备数据的 HAR 需要使用计算机视觉技术，因此不在此综述的范围内。因此，本节回顾了基于可穿戴传感器的 HAR 方法。有关基于视觉的 HAR 的综述，请参阅 Kong 和 Fu [190] 或 Zhang 等人 [191]。

可穿戴设备中使用的主要传感器是加速度计、陀螺仪和磁传感器 [192]，它们各自收集随时间变化的三维空间数据。惯性测量单元 (IMU) 是将所有三种传感器组合在一个单元中的可穿戴设备 [193, 194]。可穿戴设备研究通常从位于身体不同部位的多个 IMU 收集数据 [195, 196]。为了创建适合 HAR 建模的数据集，传感器数据被分成（通常大小相等）的时间窗口 [197]。然后，任务是学习一个函数，该函数将每个时间窗口的多变量传感器数据映射到一组活动。因此，这些数据形成了适合于 TSC 的多变量时间序列。

鉴于我们调查的范围很广，本节必然只提供一个使用深度学习进行 HAR 的研究的简要概述。然而，有几个调查提供了对机器学习和深度学习在 HAR 中应用的更深入的综述。Lara 和 Labrador [197] 提供了对 HAR 的全面介绍，包括使用的机器学习方法以及主要问题和挑战。Nweke 等人 [3] 和 Wang 等人 [4] 都提供了深度学习方法的总结，强调了它们的优点和局限性。Chen 等人 [5] 讨论了 HAR 中的挑战以及用于解决每个挑战的适当深度学习方法。他们还提供了公开可用的 HAR 数据集的完整列表。Gu 等人 [198] 专注于深度学习方法，回顾了预处理和评估技术以及深度学习模型。

用于 HAR 的深度学习方法包括 CNN 和 RNN，以及混合 CNNRNN 模型。虽然一些模型包含注意力模块，但我们没有发现任何研究提出完整的注意力或 Transformer 模型。表 5 提供了对所回顾的研究和构建的模型类型的总结。Hammerla 等人 [199] 比较了用于 HAR 的几种深度学习模型，包括三种 LSTM 变体、一个 CNN 模型和一个 DNN 模型。他们发现，双向 LSTM 在长期效应很重要的自然数据集上表现最佳。然而，他们发现一些应用需要关注短期运动模式，并建议 CNN 更适合这些应用。因此，对所有模型类型的研究都有利于 HAR 应用模型的持续开发。

本节回顾的许多论文都使用了常用的数据集来构建和评估他们的模型。

表 5. HAR 深度学习模型总结

| 模型 | 年份 | 嵌入 (Embedding) | 其他特征 |
| :--: | :--: | :--: | :--: |
| Zeng et al. [200] | 2014 | CNN |  |
| DCNN [201] | 2015 | CNN | 离散傅里叶变换 (Discrete Fourier transform) |
| Yang et al. [202] | 2015 | CNN |  |
| DeepConvLSTM [192] | 2016 | CNN, LSTM |  |
| Hammerla et al. [199] | 2016 | CNN, LSTM | 双向 (Bi-directional) |
| Ronao and Cho [203] | 2016 | CNN |  |
| Guan and Plötz [204] | 2017 | LSTM | 集成 (Ensemble) |
| Lee et al. [205] | 2017 | CNN |  |
| Murad and Pyun [206] | 2017 | LSTM | 单向和双向 (Uni- and bi-directional) |
| Ignatov [207] | 2018 | CNN | 统计特征 (Statistical features) |
| Moya Rueda et al. [208] | 2018 | CNN |  |
| Yao et al. [209] | 2018 | CNN | 全卷积 (Fully convolutional) |
| Zeng et al. [210] | 2018 | LSTM | 2 个注意力层 (2 attention layers) |
| AttnSense [211] | 2019 | CNN, GRU | 快速傅里叶变换 (Fast Fourier transform), 2 个注意力层 (2 attention layers) |
| InnoHAR [212] | 2019 | CNN, GRU | Inception |
| Zhang et al. [213] | 2020 | CNN | 注意力 (Attention) |
| Challa et al. [214] | 2021 | CNN, LSTM | 双向 (Bi-directional) |
| CNN-biGRU [215] | 2021 | CNN, GRU | 双向 (Bi-directional) |
| DEBONAIR [216] | 2021 | ConvLSTM |  |
| Mekruksavanich and | 2021 | CNN, LSTM |  |
| Jitpattanakul [217] |  |  |  |
| Mekruksavanich and | 2021 | CNN, LSTM | 集成 (Ensemble) |
| Jitpattanakul [218] |  |  |  |
| Nafea et al. [219] | 2021 | CNN, LSTM | 双向 (Bi-directional) |
| Singh et al. [220] | 2021 | CNN, LSTM | 注意力 (Attention) |
| Wang et al. [221] | 2022 | CNN |  |
| Xu et al. [222] | 2022 | CNN, Resnet | 可变形卷积 (Deformable convolutions) |

7.1.1 卷积神经网络。用于 HAR 的最常见的卷积核类型之一是 $k \times 1$ 核。该核将 *k* 个时间步长卷积在一起，依次沿着输入特征中的每个时间序列移动 [221]，因此虽然权重在输入特征之间共享，但特征之间没有混合。来自最终卷积层的输出被展平，并在进行最终分类之前由全连接层处理。Ronao 等人 [203] 对用于 HAR 的 CNN 模型进行了全面的评估，评估了改变层数、滤波器和滤波器大小的效果。输入数据是从智能手机加速度计和陀螺仪传感器收集的。Ignatov [207] 使用了一个单层 CNN，并在传递到全连接层之前，用统计特征增强了提取的特征。该架构对于短时间序列（1 秒）有效，因此对于实时活动建模很有用。上述方法的一个缺点是它强制在所有输入特征之间共享权重。这可能不是最佳的，尤其是在使用从多个设备收集的数据时。在这种情况下，为每个设备使用单独的 CNN [208] 允许独立加权特征。类似地，由于每个传感器通常是三轴的，因此可以为每个轴使用单独的 CNN [200, 213]。然后，将每个 CNN 提取的特征连接起来，并通过全连接层 [200] 或注意力头 [213] 进行处理。


虽然以上两种方法是最常见的，但其他研究也提出了用于HAR（*Human Activity Recognition*，人类活动识别）的替代CNN（*Convolutional Neural Networks*，卷积神经网络）。DCNN [201] 使用离散傅里叶变换（*Discrete Fourier Transform*）预处理传感器数据，将IMU（*Inertial Measurement Unit*，惯性测量单元）数据转换为频率信号，然后使用二维卷积来提取组合的时间和频率特征。Lee et al. [205] 将三轴加速度计数据预处理为幅度向量，然后由具有不同内核大小的CNN并行处理，从而提取不同尺度的特征。Xu et al. [222] 在2D-CNN和ResNet模型中使用了可变形卷积（*deformable convolutions*）[223]，发现这些模型比其非可变形的对应模型表现更好。Yao et al. [209] 提出了一种使用2D时间和特征卷积的全卷积模型。他们的模型有两个优点：(1) 它可以处理任意长度的输入序列；(2) 它可以为每个时间步长做出预测，这避免了将数据预处理成窗口的需要，并且可以检测活动之间的转换。
7.1.2 循环神经网络。已经提出了几种用于HAR的LSTM（*Long Short-Term Memory*，长短期记忆网络）模型。Murad and Pyun [206] 设计并比较了三个多层LSTM：一个单向LSTM，一个双向LSTM和一个“级联”LSTM，它具有一个双向第一层，然后是单向层。在每种情况下，来自所有时间步长的输出都用作分类层的输入。Zeng et al. [210] 在LSTM中添加了两个注意力层：LSTM之前的传感器注意力层和LSTM之后的时间注意力层。他们包括一个称为“连续注意力”的正则化项，以平滑注意力权重之间的过渡。Guan and Plötz [204] 通过在每个训练周期保存模型，然后根据验证集结果选择最佳“M”模型，从而创建了一个LSTM模型的集成，从而旨在减少模型方差。
7.1.3 混合模型。许多最近的研究都集中在混合模型上，将CNN和RNN（*Recurrent Neural Networks*，循环神经网络）结合在一起。DeepConvLSTM [192] 包含四个时间卷积层，然后是两个LSTM层，作者发现它的性能优于等效的CNN（用全连接层替换LSTM层）。由于LSTM层比全连接层具有更少的参数，因此DeepConvLSTM模型也小得多。Singh et al. [220] 使用CNN对空间数据（即，每个时间戳的传感器读数）进行编码，然后使用单个LSTM层对时间数据进行编码，然后使用自注意力层对时间步长进行加权。他们发现该模型的性能优于在CNN层中使用时间卷积的等效模型。Challa et al. [214] 提出并行使用三个具有不同内核大小的1D-CNN，然后是两个双向LSTM层和一个全连接层。Nafea et al. [219] 还使用了具有不同内核大小的1D-CNN和双向LSTM。但是，他们为CNN和LSTM使用了单独的分支，合并了每个分支中提取的特征，以用于最终的全连接层。Mekruksavanich and Jitpattanakul [217] 将四层CNN-LSTM模型与较小的CNN-LSTM模型和LSTM模型进行了比较，发现额外的卷积层提高了性能，超过了较小的模型。DEBONAIR [216] 是另一个多层模型。它使用并行的1D-CNN，每个1D-CNN具有不同的内核、滤波器和池化大小，以提取与不同类型的活动相关的不同类型的特征。这些特征之后是一个组合的1DCNN，然后是两个LSTM层。Mekruksavanich and Jitpattanakul [218] 集成了四个不同的模型：CNN、LSTM、CNN-LSTM和ConvLSTM模型。他们的目标是为生物特征用户识别（*boimetric user identification*）生成一个模型，该模型不仅可以识别正在执行的活动，还可以识别执行该活动的参与者。

一些混合模型使用GRU（*Gated Recurrent Unit*，门控循环单元）代替LSTM。InnoHAR [212] 是一个修改后的DeepConvLSTM [192]，用inception层替换了四个CNN层，用GRU层替换了两个LSTM层。作者发现，此inception模型的性能优于原始DeepConvLSTM模型和直接CNN模型[202]。AttnSense [211] 使用快速傅里叶变换（*Fast Fourier transform*）生成频率特征，然后针对每个时间步长分别进行卷积。

注意力层用于对提取的频率特征进行加权。然后，这些特征通过具有时间注意力的GRU，以提取时间特征。CNN-BiGRU [215] 使用CNN层从传感器数据中提取空间特征，然后使用一个或多个GRU层提取时间特征。该模型的最后一部分是一个全连接模块，由一个或多个隐藏层和一个softmax输出层组成。

# 7.2 卫星地球观测

自从NASA于1972年发射第一颗Landsat卫星[224]以来，地球观测卫星一直在记录地球表面的图像，提供了50年的连续地球观测数据，可用于估计环境变量，从而告知我们地球的状态。卫星上的仪器记录来自地球表面和植被的反射或发射的电磁辐射[225]。来自这些仪器的定期、重复的观测形成了卫星图像时间序列（SITS），可用于分析某些变量的动态特性，例如植物物候学。用于SITS分析的主要模态是多光谱光谱仪和光谱辐射计，它们观测可见光和红外频率，以及合成孔径雷达（SAR）系统，该系统发射微波信号并测量后向散射。

卫星仪器收集的原始数据需要在机器学习中使用之前进行预处理。这通常由数据提供商完成，以生成可用于分析的数据集（ARDs）。随着来自Google Earth Engine [226] 和各种数据立方体 [227, 228] 等来源的兼容ARD的可用性不断提高，结合来自多个数据源（多模态）的数据的模型正变得越来越普遍。这些数据源使得获取共同配准（*co-registered*）（空间对齐且具有相同的分辨率和投影）的数据变得简单，从而避免了复杂的预处理需求。

卫星图像时间序列可以被处理为 (1) 2D时间和光谱数据，独立处理每个像素并忽略空间维度，或者 (2) 4D数据，包括两个空间维度，在这种情况下，模型提取时空特征。后一种方法允许在像素、斑块或对象级别进行估计；但是，它需要更复杂的模型或在预处理步骤中提取空间特征。特征提取可以像提取每个波段的平均值一样简单。但是，聚类（TASSEL [229]）和基于神经网络的方法（例如Pixel-Set Encoder [230]）已被用于更复杂的特征提取。SITS深度学习最常见的用途是通过土地覆盖对地球表面进行分类，以及通过作物类型对农业用地进行分类。使用的类别范围可以从非常广泛的土地覆盖类别（例如森林、草原、农业）到特定的作物类型。其他分类任务包括识别特定特征，例如天坑 [231]、烧毁区域 [232]、洪水区域 [233]、道路 [234]、森林砍伐 [235]、植被质量 [236] 以及森林林下植被和凋落物类型 [237]。


外在回归任务不如分类任务常见，但最近的一些研究已经调查了估计植被中水分含量的方法，该水分含量由变量活燃料含水量（Live Fuel Moisture Content, LFMC）[238-241]衡量。其他回归任务包括使用混合CNN-MLP模型（将Sentinel-2图像的时间序列与单个LiDAR图像相结合）估计森林的木材体积[242]，以及使用CNN和LSTM混合模型的作物产量[243]。

已经研究了许多不同的SITS数据学习方法，这些研究使用了所有主要的深度学习架构，并针对多模态学习对其进行了调整，并在混合和集成模型中组合了这些架构。本节的其余部分回顾了已用于建模SITS数据的架构。这些论文和嵌入架构的摘要在表6中提供。

表6. SITS深度学习模型摘要

| 模型 | 年份 | 嵌入 (Embedding) | 其他特征 |
| :--: | :--: | :--: | :--: |
| 作物类型分类 (Crop type classification) |  |  |  |
| TAN [244] | 2019 | 2D-CNN和GRU | 注意力-时间 (Attention-temporal) |
| TGA [245] | 2020 | 2D-CNN | 注意力-挤压和激励 (Attention-squeeze and excitation) |
| 3D-CNN [246] | 2018 | 3D-CNN |  |
| DCM [247] | 2020 | LSTM | 自注意力 (Self-attention) |
| HierbiLSTM [248] | 2022 | LSTM | 自注意力 (Self-attention) |
| L-TAE [249] | 2020 | MLP | 注意力-时间 (Attention-temporal) |
| PSE-TAE [230, 250] | 2020 | MLP | 注意力-时间 (Attention-temporal)，可选多模态 |
| SITS-BERT [251] | 2021 |  | 预训练transformer |
| 土地覆盖分类 (Land Cover classification) |  |  |  |
| 1D-CNN [252] | 2017 | 1D-CNN和MLP | 混合模型 |
| 1D \& 2D-CNNs [253] | 2017 | 1D-CNN; 2D-CNN | 集成模型 |
| TempCNN [254] | 2019 | 1D-CNN |  |
| TASSEL [229] | 2020 | 1D-CNN | 自注意力 (Self-attention) |
| TSI [255] | 2021 | 1D-CNN; LSTM | 集成模型 |
| TWINNS [256] | 2019 | 2D-CNN和GRU | 注意力-时间 (Attention-temporal); 多模态 |
| DuPLO [257] | 2019 | 2D-CNN和GRU | 注意力-时间 (Attention-temporal) |
| Sequential RNN [258] | 2018 | 2D-FCN和LSTM | 混合模型 |
| FG-UNET [259] | 2019 | UNet和2D-CNN | 混合模型 |
| LSTM [260] | 2017 | LSTM |  |
| HOb2sRNN [261] | 2020 | GRU | 注意力-时间 (Attention-temporal) |
| OD2RNN [262] | 2019 | GRU | 注意力-时间 (Attention-temporal); 多模态 |
| SITS-Former [263] | 2022 | 3D-CNN | 预训练transformer |
| 其他分类任务 (Other classification tasks) |  |  |  |
| 森林砍伐 (Deforestation) [235] | 2022 | U-Net和LSTM | 混合模型 |
| 洪水检测 (Flood detection) [233] | 2020 | Resnet和GRU | 混合模型 |
| 森林林下植被 (Forest understory) [237] | 2022 | 2D-CNN和LSTM | 集成模型 |
| 道路检测 (Road detection) [234] | 2020 | U-Net和convLSTM | 混合模型 |
| 植被质量 (Vegetation quality) [236] | 2017 | LSTM; GRU |  |
| 外在回归任务 (Extrinsic regression tasks) |  |  |  |
| TempCNN-LFMC [239] | 2021 | 1D-CNN |  |
| Multi-tempCNN [240] | 2022 | 1D-CNN | 多模态，集成模型 |
| LFMC估计 (LFMC estimation) [238] | 2020 | LSTM | 多模态 |
| LFMC估计 (LFMC estimation) [241] | 2022 | 1D-CNN和LSTM | 多模态，混合，集成 |
| MLDL-net [243] | 2020 | 2D-CNN和LSTM | 混合模型 |
| SSTNN [264] | 2021 | 3D-CNN和LSTM | 混合模型 |
| MMFVE [242] | 2022 | 2D-CNN | 混合模型 |

7.2.1 循环神经网络（Recurrent Neural Networks, RNNs）。Ienco等人[260]是最早使用RNN进行土地覆盖分类的论文之一，他们表明LSTM模型优于非深度学习方法，如随机森林（Random Forest, RF）和支持向量机（Support Vector Machines, SVMs）。然而，他们也表明，如果使用LSTM模型提取的特征进行训练，RF和SVM的性能都会提高，并且在某些情况下，它们比直接使用LSTM模型更准确。Rao等人[238]使用外在回归LSTM模型来估计美国西部的LFMC。

然而，更常见的是，RNN与注意力层结合使用，以使模型能够专注于最重要的时间步。OD2RNN模型[262]使用单独的GRU层，然后使用注意力层来处理Sentinel-1和Sentinel-2数据，组合了每个来源提取的特征，用于最终的全连接层。HOb2sRNN [261]通过使用土地覆盖分类的层次结构来改进OD2RNN；该模型使用广泛的土地覆盖分类进行预训练，然后使用更细粒度的分类进行进一步训练。DCM [247]和HierbiLSTM [248]都使用双向LSTM，在两个方向上处理时间序列，然后使用自注意力transformer用于像素级作物绘图模型。所有这些研究都发现，与直接使用GRU或LSTM模型相比，添加注意力层可以提高模型性能。
7.2.2 卷积神经网络（Convolutional Neural Networks, CNNs）。虽然许多作者声称RNN在土地覆盖和作物类型分类方面优于CNN，但这些比较大多是与2DCNN进行的，后者忽略了SITS数据的时间顺序[254]。然而，其他研究表明，使用1D-CNN提取时间信息和使用3D-CNN提取时空信息都是从SITS数据中学习的有效方法。TempCNN [254]由三个1D卷积层组成。来自最终卷积层的输出通过一个全连接层，然后是最终的softmax分类层。TASSEL [229]是TempCNN针对OBIA分类的改编，使用TempCNN模型处理从对象中提取的特征，然后使用注意力层来加权卷积特征。TempCNN也已针对外在回归进行了改编[239]，并用于LFMC估计[239-241]。

2D-CNN主要用于提取像素和对象分类的空间或时空特征。模型输入通常是4D，并且数据在空间上进行卷积，使用两种主要方法来处理时间维度。在第一种方法中，每个时间步分别进行卷积，提取的特征在模型的后期阶段合并[245]。在第二种方法中，时间步和通道被展平以形成一个大的多元图像[242, 253]。FG-UNet [259]是一个完全卷积模型，它结合了上述两种方法，首先将时间步按三个一组分组，以生成具有30个通道（10个光谱$\times 3$个时间）的图像，这些图像通过U-Net和2D-CNN层。Ji等人[246]使用3D-CNN将空间和时间维度卷积在一起，结合了1D-CNN和2D-CNN的优势。该研究发现，3D-CNN作物分类模型的性能明显优于2D-CNN，再次表明了时间特征的重要性。另一项研究SSTNN [264]通过使用3D-CNN卷积空间和光谱维度，提取每个时间步的时空光谱特征，从而获得了良好的作物产量预测结果。然后，这些特征由LSTM层处理以执行时间建模。


7.2.3 Transformer和注意力模型。作为在CNN或RNN中包含注意力层的一种替代方案，一些研究设计了仅使用注意力层处理时间信息的模型。PSE-TAE [230] 使用了一种改进的transformer，称为时间注意力编码器 (TAE, Temporal Attention Encoder) 进行作物制图，发现TAE的性能优于CNN或RNN。L-TAE [249] 用一个轻量级transformer替换了TAE，该transformer在计算上更有效，并且比完整的TAE更准确。Ofori-Ampofo等人 [250] 使用Sentinel-1和Sentinel-2数据，将TAE模型调整为多模态输入，用于作物类型制图。Rußwurm和Körner [265] 比较了自注意力模型与RNN和CNN架构。他们发现该模型比RNN或CNN更具噪声鲁棒性，并认为自注意力适用于处理原始的、受云影响的卫星数据。

基于预训练transformer在自然语言处理方面的成功，例如BERT [94]，已经提出了用于地球观测任务的预训练transformer [251]。地球观测任务特别适合预训练模型，因为可以很容易地获得大量的地球观测数据，而标记数据可能难以获得 [266]，尤其是在偏远地区。SITS-BERT [251] 是BERT [94] 的一种改编，用于基于像素的SITS分类。对于pretext任务，将随机噪声添加到像素，并训练模型以识别和删除此噪声。然后，对预训练模型进行进一步训练，以完成所需的任务，例如作物类型或土地覆盖制图。SITS-Former [263] 通过使用3D-Conv层来编码空间-光谱信息，然后将其传递到时间注意力层，从而修改了用于patch分类的SITS-BERT。用于SITS-Former的pretext任务是预测随机掩盖的像素。
7.2.4 混合模型。混合模型的一个常见用途是使用CNN提取空间特征，并使用RNN提取时间特征。Garnot等人 [267] 比较了一个直接的2D-CNN模型（因此忽略了时间方面），一个直接的GRU模型（因此忽略了空间方面）以及一个组合的2D-CNN和GRU模型（因此同时使用了空间和时间信息），发现组合模型给出了最佳结果，表明空间和时间维度都为土地覆盖制图和作物分类提供了有用的信息。DuPLO [257] 是最早利用这种方法的模型之一，它并行运行CNN和ConvGRU模型，然后使用完全连接的网络融合输出，以用于最终的分类器。在训练期间，使用了每个组件的辅助分类器来增强判别能力。TWINNS [256] 将DuPLO扩展到多模态模型，使用了Sentinel-1 (SAR) 和 Sentinel-2 (Optical) 图像的时间序列。每个模态都由单独的CNN和convGRU模型处理，然后融合来自所有四个模型的输出特征以进行分类。

其他混合模型包括Li等人 [244]，他们使用CNN对Landsat-8和Sentinel-2图像进行空间和光谱统一，然后由GRU处理。MLDL-Net [243] 是一个2DCNN外在回归模型，使用CNN提取时间步长特征，然后将其传递到LSTM模型以提取时间特征。完全连接的层组合了特征集以预测作物产量。Rußwurm和Körner [258] 首先使用双向LSTM提取时间特征，然后使用完全卷积的2D-CNN来合并空间信息并对输入patch中的每个像素进行分类。
7.2.5 集成模型。集成DL模型的最简单方法之一是训练多个同构模型，这些模型仅在随机权重初始化方面有所不同 [268]。Di Mauro等人 [252] 通过平均softmax预测，集成了100个具有不同权重初始化的LULC模型。他们发现这产生了一个更稳定，更强大的分类器，其性能优于各个模型。Multi-tempCNN [240] 是一种用于LFMC估计的模型，是用于外在回归的同构模型集成。作者认为，作为一项额外的好处，各个模型预测的方差可用于获得估计值的不确定性度量。TSI [255] 也集成了一组同构模型，但是没有依靠随机权重初始化来引入模型多样性，而是对时间序列进行分割，并针对每个分段训练模型。

其他方法创建异构模型的集成。Kussul等人 [253] 比较了用于土地覆盖分类的1D-CNN和2D-CNN模型的集成。集成中的每个模型都使用了不同数量的滤波器，因此发现了对分类有用的不同特征集。Xie等人 [241] 集成了三个异构模型-因果时间卷积神经网络 (TCN, Causal Temporal Convolutional Neural Network)，LSTM和混合TCN-LSTM模型-用于估计LFMC的外在回归模型。使用堆叠 (stacking) [269] 创建集成。作者将此方法与boosting其TCN-LSTM模型进行了比较，使用Adaboost [270] 创建了一个三成员集成，发现堆叠一组不同的模型优于boosting。
7.2.6 EO调查和评论。本调查是极少数专门针对使用SITS数据的深度学习TSC和TSER任务的部分之一。但是，还有其他评论提供了有关相关主题的更多信息。Gomez等人 [271] 是一篇较早的评论，强调了SITS数据在土地覆盖分类中的重要作用。Zhu等人 [272] 回顾了遥感DL的进展和挑战，以及可用于帮助DL应对人类面临的一些主要挑战的可用资源。Ma等人 [273] 研究了深度学习在使用遥感数据的地球观测中的作用。它涵盖了广泛的任务，包括图像融合，图像分割和基于对象的分析以及分类任务。Yuan等人 [274] 提供了遥感DL应用的综述，比较了DL与环境变量物理建模的作用，并强调了遥感DL中需要解决的挑战。Chaves等人 [275] 回顾了最近使用Landsat 8和/或Sentinel-2数据进行土地覆盖制图的研究。尽管未专注于SITS DL方法，但该评论指出这些方法的重要性日益提高。Moskolai等人 [276] 提供了使用SITS数据的DL进行预测应用的综述，该综述分析了与分类和预测相关的主要DL架构。


# 8 结论

总而言之，这篇综述文章讨论了用于时间序列分类和外在回归任务的各种深度网络架构，包括多层感知器、卷积神经网络、循环神经网络和基于注意力机制的模型。我们还强调了为提高这些模型在时间序列任务上的性能而进行的改进。此外，我们还讨论了时间序列分类和回归的两个关键应用：人类活动识别和卫星地球观测。总的来说，使用深度网络架构和改进措施，使得时间序列分类领域取得了显著进展，并将继续成为解决各种实际问题的关键。我们希望这篇综述能够激发更多使用深度学习技术进行时间序列分类和外在回归的研究。此外，我们提供了一个精心策划的资源集合，可在 https://github.com/Navidfoumani/TSC_Survey 获取，以进一步支持研究社区。

## 附录

## A 非深度学习时间序列分类

在本节中，我们旨在简要介绍 TSC 领域并讨论其当前状态。我们建议感兴趣的读者参考 "bake-off" 论文 [11, 25, 26]，其中更详细地描述了 TSC 方法并对其进行了基准测试。

TSC 的研究始于基于距离的方法，该方法寻找时间序列形状中具有区分性的模式。基于距离的方法通常包括将 1-最近邻 (1NN) 分类器与时间序列距离度量相结合 [277, 278]。当使用标准距离测量（如欧几里得距离 [277]）测量时间序列之间的距离时，时间序列中的微小失真可能导致错误的匹配。时间序列距离度量旨在通过对齐两个时间序列来补偿这些失真，从而使两者之间的对齐成本最小化。文献中提出了许多时间序列距离；其中，$D T W$ 距离是许多时间序列任务中最受欢迎的选择之一，因为它在对齐两个时间序列时具有直观性和有效性。几十年来，1NN-DTW 一直是 TSC 的首选方法。然而，通过比较几种时间序列距离度量，[277] 中的工作表明，截至 2015 年，当与 1NN 分类器一起使用时，没有哪一种距离能够显著优于 DTW。最近的 Amerced DTW [279] 距离是第一个明显比 $D T W$ 更准确的距离。这些具有不同距离的单个 1NN 分类器可以组合在一起以创建一个集成，例如弹性距离集成 (Ensemble of Elastic distances, EE)，该集成显著优于它们中的每一个 [277, 278]。然而，由于大多数距离的复杂度为 $O\left(L^{2}\right)$，其中 $L$ 是序列的长度，因此执行最近邻搜索变得非常昂贵。因此，基于距离的方法被认为是 TSC 最慢的方法之一 [280, 281]。

由于 EE 的出现，最近的研究主要集中在开发显著优于 1NN-DTW 的集成方法 [278, 280-290]。这些方法要么使用基于树的方法的集成 [289, 290]，要么使用不同类型的判别分类器的集成，例如具有多个距离的 NN 和在单个或多个特征空间上的 SVM [282, 285-287]。所有这些方法都具有一个共同的属性——数据转换阶段，其中时间序列被转换为新的特征空间，例如 shapelets 变换 [286] 或 DTW 特征 [285]。利用这一概念导致了基于转换的集成的分层投票集合 (Hierarchical Vote Collective of Transformation-based Ensembles, HIVE-COTE) 的开发 [281, 284]。HIVE-COTE 是 TSC 的元集成，并从多个领域的集成分类器中形成其集成。自 2016 年推出以来 [284]，HIVE-COTE 经历了几次迭代。最近，提出了最新的 HIVE-COTE 版本，HIVE-COTEv2.0 (HC2) [281]。它由四个集成成员组成，每个成员都是当时各自领域中最先进的技术。目前，它是单变量和多变量 TSC 任务中最准确的分类器之一 [281]。尽管在相对较小的 26 个多变量和 142 个单变量 TSC 基准数据集上具有准确性，但 HC2 在具有长时序的大型数据集以及具有大量通道的数据集上表现不佳。

已经完成了各种工作来加速 TSC 方法，而不会牺牲准确性 [14, 278, 291-295]。最近的一项突破是 Rocket [14] 的开发，它能够在 4 小时内处理 109 个单变量时间序列数据集，而之前最快的方法需要几天时间。Rocket 利用大量随机卷积滤波器从每个序列中提取可能与分类序列相关的特征。然后将这些特征传递给线性模型进行分类。Rocket 已经过改进，变得更快 (Minirocket [291]) 并且更准确 (Multirocket [292] 和 Hydra [293])。Hydra 与 Multirocket 结合使用时，现在是 TSC 最快和最准确的方法之一。


# B 用于时间序列的 DNN 架构

在本节中，我们将提供一个基于深度学习的 TSC (Time Series Classification，时间序列分类) 模型的描述性概述。重点是阐明它们的架构，并概述它们对时间序列数据特定特征的适应性。

## B. 1 卷积神经网络 (CNNs)

文献中已经提出了许多 CNN 架构的变体，但它们的主要组成部分非常相似。以 LeNet-5 [296] 为例，它由三种类型的层组成：卷积层、池化层和全连接层。卷积层的目的是学习输入的特征表示。图 2(a) 显示了 t-LeNet 网络的架构，它是 LeNet 的时间序列特定版本。该图显示，卷积层由几个卷积核（或滤波器）组成，用于计算不同的特征图。特别地，特征图的每个神经元都连接到前一层中相邻神经元的区域，称为感受野 (receptive field)。可以通过首先将输入与学习的内核进行卷积，然后将逐元素非线性激活函数应用于卷积结果来创建特征图。重要的是要注意，输入的**所有**空间位置共享每个特征图的内核，并且使用多个内核来获得整个特征图。

第 $l$ 层的第 $k$ 个特征图在位置 $(i, j)$ 处的特征值由下式获得：

$$
Z_{i, j, k}^{l}=\mathbf{W}_{k}^{l T} \mathbf{A}_{i, j}^{l-1}+b_{k}^{l}
$$

其中 $\mathbf{W}_{k}^{l}$ 和 $b_{k}^{l}$ 分别是第 $l$ 层的第 $k$ 个滤波器的权重向量和偏置项，$\mathbf{A}_{i, j}^{l-1}$ 是以第 $l$ 层的 $(i, j)$ 位置为中心的输入块。请注意，生成特征图 $Z_{\ldots, k}^{l}$ 的内核 $\mathbf{W}_{k}^{l}$ 是共享的。权重共享机制具有几个优点，例如降低模型复杂性并使网络更易于训练。令 $f($.$) 表示非线性激活函数。卷积特征$Z_{i, j, k}^{l}$ 的激活值可以计算为

$$
\mathrm{A}_{i, j, k}^{l}=f\left(Z_{i, j, k}^{l}\right)
$$

最常见的激活函数是 sigmoid、tanh 和 ReLU [297]。如图 2(a) 所示，池化层通常放置在两个卷积层之间，以降低特征图的分辨率并实现平移不变性 (shift invariance)。在几个卷积阶段之后——包含卷积、激活和池化的块称为卷积阶段——可能有一个或多个全连接层，旨在执行高级推理。如第 3.1 节所述，前一层中的每个神经元都连接到当前层中的每个神经元，以生成全局语义信息。在 CNN 的最后一层中，有输出层，其中 Softmax 算子通常用于分类任务 [40]。

![img-1.jpeg](images/img-1.jpeg.png)

图 2。(a) t-LeNet 网络的架构。(b) 两层循环神经网络的架构。

# B. 2 循环神经网络 (RNN)

RNN 是一种专门设计用于处理时间序列和其他序列数据的神经网络。RNN 在概念上与 FFN (Feedforward Neural Network，前馈神经网络) 相似。虽然 FFN 将固定大小的输入映射到固定大小的输出，但 RNN 可以处理可变长度的输入并产生可变长度的输出。这种能力是通过在各个层之间通过有向连接随时间共享参数来实现的。用于 TSC 的 RNN 模型可以根据其输出分为序列到序列 (sequence to sequence) 或序列到一 (sequence to one)。图 2(b) 显示了 RNN 模型的序列到序列架构，每个输入子序列都有一个输出。另一方面，在序列到一架构中，仅使用 $y^{T}$ 做出决策，而忽略其他输出。

在每个时间步 $t$，RNN 维护一个隐藏向量 $h$，它按如下方式更新 [298, 299]：

$$
h_{t}=\tanh \left(W h_{t-1}+I x^{t}\right)
$$

其中 $X=\left\{x^{1}, \ldots, x^{t-1}, x^{t}, \ldots, x^{T}\right\}$ 包含所有观测值，$\tanh$ 表示双曲正切函数，循环权重和投影矩阵分别用 $W$ 和 $I$ 表示。隐藏到隐藏的连接也模拟了短期时间依赖性。隐藏状态 $h$ 用于进行预测，如下所示

$$
y^{t}=\sigma_{x}\left(W h_{t-1}\right)
$$

其中 $\sigma_{s}$ 是 softmax 函数，并提供可能的类别的归一化概率分布。如图 2(b) 所示，隐藏状态 $h$ 可用于堆叠 RNN，以构建更深的网络：

$$
h_{t}^{l}=\sigma\left(W h_{t-1}^{l}+I h_{t}^{l-1}\right)
$$

其中 $\sigma$ 是 Logistic sigmoid 函数。作为将每个时间步长馈送到 RNN 的替代方法，可以将数据划分为 $\omega$ 个观测值的时间窗口，可以选择可变重叠。每个时间窗口都标有 $\omega$ 窗口内的多数响应标签。

# B. 3 基于注意力机制的模型

B.3.1 自注意力 (Self-attention)。注意力机制由 [300] 引入，用于提高神经机器翻译中编码器-解码器模型 [301] 的性能。神经机器翻译中的编码器-解码器将源语句编码为潜在空间中的向量，并将潜在向量解码为目标语言语句。如图 3(a) 所示，注意力机制允许解码器通过上下文向量 $c_{t}$ 注意源的各个片段。对于该模型，通过将当前目标隐藏状态 $h_{t}$ 与每个源隐藏状态 $\widetilde{h}_{s}$ 进行比较，可以得出可变长度的注意力向量 $\alpha_{t}$，该向量等于源时间步的数量，如下所示 [302]：

$$
\alpha_{t}(s)=\frac{\exp \left(\operatorname{score}\left(h_{t}, \widetilde{h}_{s}\right)\right)}{\sum_{s^{\prime}} \exp \left(\operatorname{score}\left(h_{t}, \widetilde{h}_{s^{\prime}}\right)\right)}
$$

术语 score 称为对齐模型 (alignment model)，用于将目标隐藏状态 $h_{t}$ 与每个源隐藏状态 $\widetilde{h}_{s}$ 进行比较，并将结果归一化以产生注意力权重（源位置上的分布）。评分函数有多种选择：

$$
\operatorname{score}\left(h_{t}, \widetilde{h}_{s}\right)=\left\{\begin{array}{l}
h_{t}^{T} W \widetilde{h}_{s} \\
v_{\alpha}^{T} \tanh \left(W_{\alpha}\left[h_{t} ; \widetilde{h}_{s}\right]\right)
\end{array}\right.
$$

这些分数会影响注意力分布，从而影响模型在预测期间如何关注输入序列的不同部分。如上所示，评分函数被参数化为 FFN，该 FFN 与模型的所有其他组件联合训练。该模型直接计算软注意力 (soft attention)，允许成本函数的梯度反向传播 [300]。给定对齐向量作为权重，上下文向量 $c_{t}$ 计算为所有源隐藏状态的加权平均值：

$$
c_{t}=\sum_{s} \alpha_{t s} \widetilde{h}_{s}
$$


因此，计算路径从$h_{t} \rightarrow \alpha_{t} \rightarrow c_{t} \rightarrow \widetilde{h}_{t}$开始，然后使用Softmax函数[302]进行预测。 请注意，$\widetilde{h}_{t}$是一个精炼的隐藏状态，它结合了原始隐藏状态$h_{t}$和通过注意力机制获得的上下文信息$c_{t}$。
B.3.2 Transformers（Transformer模型）. 与自注意力（self-attention）和其他有竞争力的神经序列模型类似，最初为自然语言处理开发的transformer（以下简称 vanilla transformer）具有编码器-解码器结构，该结构接收来自源语言的单词序列作为输入，然后在目标语言中生成翻译[93]。 编码器和解码器都由多个相同的块组成。 每个编码器块由一个多头自注意力（multi-head self-attention）模块和一个位置前馈网络（position-wise FFN）组成，而每个解码器块在多头自注意力模块和位置前馈网络之间插入交叉注意力（cross-attention）模型。 与RNN不同，transformer不使用循环，而是使用输入嵌入中的位置编码（positional encoding）来建模序列信息。

![img-2.jpeg](images/img-2.jpeg.png)

图3. (a) 自注意力机制. (b) 多头注意力块.

Transformer架构基于使用点积查找各种输入段之间的关联或相关性。 如图3(b)所示，transformer中的注意力操作首先从输入$x_{i}$构建三个不同的线性加权向量，分别称为query $\left(q_{i}\right)$、key $\left(k_{i}\right)$和value $\left(v_{i}\right)$：

$$
\mathbf{q}_{i}=W_{q} \mathbf{x}_{i}, \quad \mathbf{k}_{i}=W_{k} \mathbf{x}_{i}, \quad \mathbf{v}_{i}=W_{v} \mathbf{x}_{i}
$$

其中$W_{q}, W_{k}$和$W_{v}$是可学习的权重矩阵。 输出向量$\mathbf{z}_{i}$由下式给出

$$
\mathbf{z}_{i}=\sum_{j} \operatorname{softmax}\left(\frac{\mathbf{q}_{i}^{T} \mathbf{k}_{j}}{\sqrt{d_{q}}}\right) \mathbf{v}_{i}
$$

请注意，值向量$\mathbf{v}_{i}$的权重取决于位置$i$处的query向量$\mathbf{q}_{j}$和位置$j$处的key向量$\mathbf{k}_{j}$之间的映射相关性。 点积的值往往随着query和key向量大小的增加而增长。 由于softmax函数对大值敏感，因此注意力权重按query和key向量大小的平方根$d_{q}$进行缩放。 输入数据可能包含多个级别的相关性信息，并且学习过程可能会受益于以多种不同的方式处理输入数据。 引入了多个注意力头（attention heads），它们并行地对相同的输入进行操作，并使用不同的权重矩阵$W_{q}, W_{k}$和$W_{v}$来提取输入数据之间各种级别的相关性。

# REFERENCES

[1] Qiang Yang and Xindong Wu. 2006. 10 challenging problems in data mining research. International Journal of Information Technology & Decision Making 5, 4 (2006), 597-604.
[2] Philippe Esling and Carlos Agon. 2012. Time-series data mining. ACM Computing Surveys (CSUR) 45, 1 (2012), 1-34.
[3] Henry Friday Nweke, Ying Wah Teh, Mohammed Ali Al-Garadi, and Uzoma Rita Alo. 2018. Deep learning algorithms for human activity recognition using mobile and wearable sensor networks: State of the art and research challenges. Expert Systems with Applications 105 (2018), 233-261. DOI :http://dx.doi.org/10.1016/j.eswa.2018.03.056
[4] Jindong Wang, Yiqiang Chen, Shuji Hao, Xiaohui Peng, and Lisha Hu. 2019. Deep learning for sensor-based activity recognition: A survey. Pattern Recognition Letters 119 (2019), 3-11. DOI : http://dx.doi.org/10.1016/j.patrec.2018.02.010
[5] Kaixuan Chen, Dalin Zhang, Lina Yao, Bin Guo, Zhiwen Yu, and Yunhao Liu. 2021. Deep learning for sensor-based human activity recognition: Overview, challenges, and opportunities. ACM Computing Surveys (CSUR) 54, 4 (2021), $1-40$. DOI : http://dx.doi.org/10.1145/3447744
[6] Robin Tibor Schirrmeister, Jost Tobias Springenberg, Lukas Dominique Josef Fiederer, Martin Glasstetter, Katharina Eggensperger, Michael Tangermann, Frank Hutter, Wolfram Burgard, and Tonio Ball. 2017. Deep learning with convolutional neural networks for EEG decoding and visualization. Human Brain Mapping 38, 11 (2017), 5391-5420.
[7] A. Rajkomar, E. Oren, K. Chen, A. M. Dai, N. Hajaj, M. Hardt, P. J. Liu, X. Liu, J. Marcus, M. Sun, P. Sundberg, H. Yee, K. Zhang, Y. Zhang, G. Flores, G. E. Duggan, J. Irvine, Q. Le, K. Litsch, A. Mossin, J. Tansuwan, D. Wang, J. Wexler, J. Wilson, D. Ludwig, S. L. Volchenboum, K. Chou, M. Pearson, S. Madabushi, N. H. Shah, A. J. Butte, M. D. Howell, C.

Cui, G. S. Corrado, J. Dean. 2018. Scalable and accurate deep learning with electronic health records. NPJ Digit Med. 2018 May 8, 1 (2018), 18. DOI:10.1038/s41746-018-0029-1. PMID: 31304302; PMCID: PMC6550175.
[8] Anthony Bagnall, Hoang Anh Dau, Jason Lines, Michael Flynn, James Large, Aaron Bostrom, Paul Southam, and Eamonn Keogh. 2018. The UEA multivariate time series classification archive, 2018. arXiv preprint:1811.00075 (2018).
[9] Hoang Anh Dau, Anthony Bagnall, Kaveh Kamgar, Chin-Chia Michael Yeh, Yan Zhu, Shaghayegh Gharghabi, Chotirat Ann Ratanamahatana, and Eamonn Keogh. 2019. The UCR time series archive. IEEE/CAA Journal of Automatica Sinica 6, 6 (2019), 1293-1305.
[10] Chang Wei Tan, Christoph Bergmeir, François Petitjean, and Geoffrey I. Webb. 2021. Time series extrinsic regression. Data Mining and Knowledge Discovery 35, 3 (2021), 1032-1060.
[11] Matthew Middlehurst, Patrick Schäfer, and Anthony Bagnall. 2023. Bake off redux: A review and experimental evaluation of recent time series classification algorithms. arXiv preprint arXiv:2304.13029 (2023).
[12] Hassan Ismail Fawaz, Benjamin Lucas, Germain Forestier, Charlotte Pelletier, Daniel F. Schmidt, Jonathan Weber, Geoffrey I. Webb, Lhassane Idoumghar, Pierre-Alain Muller, and François Petitjean. 2020. Inceptiontime: Finding Alexnet for time series classification. Data Mining and Knowledge Discovery 34, 6 (2020), 1936-1962.
[13] Navid Mohammadi Foumani, Chang Wei Tan, Geoffrey I. Webb, and Mahsa Salehi. 2024. Improving position encoding of transformers for multivariate time series classification. Data Mining and Knowledge Discovery 38, 1 (2024), 22-48.
[14] Angus Dempster, François Petitjean, and Geoffrey I. Webb. 2020. ROCKET: Exceptionally fast and accurate time series classification using random convolutional kernels. Data Mining and Knowledge Discovery 34, 5 (2020), 1454-1495.
[15] Hassan Ismail Fawaz, Germain Forestier, Jonathan Weber, Lhassane Idoumghar, and Pierre-Alain Muller. 2019. Deep learning for time series classification: A review. Data Mining and Knowledge Discovery 33, 4 (2019), 917-963.
[16] Zhiguang Wang, Weizhong Yan, and Tim Oates. 2017. Time series classification from scratch with deep neural networks: A strong baseline. In 2017 International Joint Conference on Neural Networks (IJCNN'17). IEEE, 1578-1585.
[17] Qingsong Wen, Tian Zhou, Chaoli Zhang, Weiqi Chen, Ziqing Ma, Junchi Yan, and Liang Sun. 2022. Transformers in time series: A survey. arXiv preprint:2202.07125 (2022).
[18] Yifan Hao and Huiping Cao. 2020. A new attention mechanism to classify multivariate time series. In 29th International Joint Conference on Artificial Intelligence.


[19] George Zerveas, Srideepika Jayaraman, Dhaval Patel, Anuradha Bhamidipaty, 和 Carsten Eickhoff. 2021. A transformer-based framework for multivariate time series representation learning（基于Transformer的多元时间序列表示学习框架）. In 27th ACM SIGKDD Conference on Knowledge Discovery \& Data Mining. 2114-2124.
[20] Xiao Liu, Fanjin Zhang, Zhenyu Hou, Li Mian, Zhaoyu Wang, Jing Zhang, 和 Jie Tang. 2021. Self-supervised learning: Generative or contrastive（自监督学习：生成式或对比式）. IEEE Transactions on Knowledge and Data Engineering 35, 1 (2021), 857-876.
[21] Emadeldeen Eldele, Mohamed Ragab, Zhenghua Chen, Min Wu, Chee Keong Kwoh, Xiaoli Li, 和 Cuntai Guan. 2021. Time-series representation learning via temporal and contextual contrasting（通过时间和上下文对比的时间序列表示学习）. In Proceedings of the 30th International Joint Conference on Artificial Intelligence (IJCAI'21). 2352-2359.
[22] Chao-Han Huck Yang, Yun-Yun Tsai, 和 Pin-Yu Chen. 2021. Voice2series: Reprogramming acoustic models for time series classification（Voice2series：为时间序列分类重新编程声学模型）. In International Conference on Machine Learning. PMLR, 11808-11819.
[23] Zhihan Yue, Yujing Wang, Juanyong Duan, Tianmeng Yang, Congrui Huang, Yunhai Tong, 和 Bixiong Xu. 2022. Ts2vec: Towards universal representation of time series（Ts2vec：迈向时间序列的通用表示）. Proceedings of the AAAI Conference on Artificial Intelligence 36, 8 (2022), 8980-8987.
[24] Navid Mohammadi Foumani, Chang Wei Tan, Geoffrey I Webb, 和 Mahsa Salehi. 2023. Series2Vec: Similarity-based self-supervised representation learning for time series classification（Series2Vec：基于相似性的时间序列分类自监督表示学习）. arXiv preprint arXiv:2312.03998 (2023).
[25] Anthony Bagnall, Jason Lines, Aaron Bostrom, James Large, 和 Eamonn Keogh. 2017. The great time series classification bake off: A review and experimental evaluation of recent algorithmic advances（伟大的时间序列分类烘焙大赛：近期算法进展的回顾和实验评估）. Data Mining and Knowledge Discovery 31, 3 (2017), 606-660.
[26] Alejandro Pasos Ruiz, Michael Flynn, James Large, Matthew Middlehurst, 和 Anthony Bagnall. 2021. The great multivariate time series classification bake off: a review and experimental evaluation of recent algorithmic advances（伟大的多元时间序列分类烘焙大赛：近期算法进展的回顾和实验评估）. Data Mining and Knowledge Discovery 35, 2 (2021), 401-449.
[27] Chang Wei Tan, Christoph Bergmeir, Francois Petitjean, 和 Geoffrey I Webb. 2020. Monash university, UEA, UCR time series regression archive（莫纳什大学、UEA、UCR时间序列回归档案）. arXiv preprint:2006.10996 (2020).
[28] Martin Längkvist, Lars Karlsson, 和 Amy Loutfi. 2014. A review of unsupervised feature learning and deep learning for time-series modeling（时间序列建模的无监督特征学习和深度学习综述）. Pattern Recognition Letters 42 (2014), 11-24.
[29] Yoshua Bengio, Li Yao, Guillaume Alain, 和 Pascal Vincent. 2013. Generalized denoising auto-encoders as generative models（广义去噪自编码器作为生成模型）. Advances in Neural Information Processing Systems 26 (2013), 899-907.
[30] Qinghua Hu, Rujia Zhang, 和 Yucan Zhou. 2016. Transfer learning for short-term wind speed prediction with deep neural networks（基于深度神经网络的短期风速预测迁移学习）. Renewable Energy 85 (2016), 83-95.
[31] Joan Serrà, Santiago Pascual, 和 Alexandros Karatzoglou. 2018. Towards a universal neural network encoder for time series（迈向时间序列的通用神经网络编码器）. In CCIA. 120-129.

[32] Debrup Banerjee, Kazi Islam, Keyi Xue, Gang Mei, Lemin Xiao, Guangfan Zhang, Roger Xu, Cai Lei, Shuiwang Ji, 和 Jiang Li. 2019. A deep transfer learning approach for improved post-traumatic stress disorder diagnosis（一种用于改善创伤后应激障碍诊断的深度迁移学习方法）. Knowledge and Information Systems 60, 3 (2019), 1693-1724.
[33] Witali Aswolinskiy, René Felix Reinhart, 和 Jochen Steil. 2018. Time series classification in reservoir-and modelspace（储层和模型空间中的时间序列分类）. Neural Processing Letters 48, 2 (2018), 789-809.
[34] Eoin Brophy, Zhengwei Wang, Qi She, 和 Tomás Ward. 2023. Generative adversarial networks in time series: A systematic literature review（时间序列中的生成对抗网络：系统文献综述）. ACM Computer Surveys 55, 10, Article 199 (Feb. 2023), 31 pages. DOI : http://dx.doi.org/ $10.1145 / 3559540$
[35] Felipe Arias Del Campo, María Cristina Guevara Neri, Osslan Osiris Vergara Villegas, Vianey Guadalupe Cruz Sánchez, Humberto de Jesús Ochoa Domínguez, 和 Vicente García Jiménez. 2021. Auto-adaptive multilayer perceptron for univariate time series classification（用于单变量时间序列分类的自适应多层感知器）. Expert Systems with Applications 181 (2021), 115147.
[36] Brian Kenji Iwana, Volkmar Frinken, 和 Seiichi Uchida. 2016. A robust dissimilarity-based neural network for temporal pattern recognition（一种用于时间模式识别的基于鲁棒差异性的神经网络）. In 2016 15th International Conference on Frontiers in Handwriting Recognition (ICFHR'16). IEEE, $265-270$.
[37] Brian Kenji Iwana, Volkmar Frinken, 和 Seiichi Uchida. 2020. DTW-NN: A novel neural network for time series recognition using dynamic alignment between inputs and weights（DTW-NN：一种使用输入和权重之间动态对齐的时间序列识别的新型神经网络）. Knowledge-based Systems 188 (2020), 104971.
[38] Nuzhat Tabassum, Sujeendran Menon, 和 Agnieszka Jastrzebska. 2022. Time-series classification with SAFE: Simple and fast segmented word embedding-based neural time series classifier（使用SAFE的时间序列分类：基于简单快速分段词嵌入的神经时间序列分类器）. Information Processing \& Management 59, 5 (2022), 103044.
[39] Alex Krizhevsky, Ilya Sutskever, 和 Geoffrey E. Hinton. 2012. Imagenet classification with deep convolutional neural networks（使用深度卷积神经网络的Imagenet分类）. Advances in Neural Information Processing Systems 25 (2012), 1097-1105.
[40] Jiuxiang Gu, Zhenhua Wang, Jason Kuen, Lianyang Ma, Amir Shahroudy, Bing Shuai, Ting Liu, Xingxing Wang, Gang Wang, Jianfei Cai, et al. 2018. Recent advances in convolutional neural networks（卷积神经网络的最新进展）. Pattern Recognition 77 (2018), $354-377$.
[41] Yann LeCun, Yoshua Bengio, 和 Geoffrey Hinton. 2015. Deep learning（深度学习）. Nature 521, 7553 (2015), 436-444.
[42] Seyed Navid Mohammadi Foumani 和 Ahmad Nickabadi. 2019. A probabilistic topic model using deep visual word representation for simultaneous image classification and annotation（一种使用深度视觉词表示的概率主题模型，用于同步图像分类和注释）. Journal of Visual Communication and Image Representation 59 (2019), 195-203.
[43] Yi Zheng, Qi Liu, Enhong Chen, Yong Ge, 和 J Leon Zhao. 2014. Time series classification using multi-channels deep convolutional neural networks（使用多通道深度卷积神经网络的时间序列分类）. In International Conference on Web-age Information Management. Springer, 298-310.
[44] Jianbo Yang, Minh Nhut Nguyen, Phyo Phyo San, Xiao Li Li, 和 Shonali Krishnaswamy. 2015. Deep convolutional neural networks on multichannel time series for human activity recognition（用于人类活动识别的多通道时间序列上的深度卷积神经网络）. In 24th International Joint Conference on Artificial Intelligence.
[45] Bendong Zhao, Huanzhang Lu, Shangfeng Chen, Junliang Liu, 和 Dongya Wu. 2017. Convolutional neural networks for time series classification（用于时间序列分类的卷积神经网络）. Journal of Systems Engineering and Electronics 28, 1 (2017), 162-169.
[46] Jonathan Long, Evan Shelhamer, 和 Trevor Darrell. 2015. Fully convolutional networks for semantic segmentation（用于语义分割的全卷积网络）. In IEEE Conference on Computer Vision and Pattern Recognition. 3431-3440.
[47] Kaiming He, Xiangyu Zhang, Shaoqing Ren, 和 Jian Sun. 2016. Deep residual learning for image recognition（用于图像识别的深度残差学习）. In IEEE Conference on Computer Vision and Pattern Recognition. 770-778.
[48] Bolei Zhou, Aditya Khosla, Agata Lapedriza, Aude Oliva, 和 Antonio Torralba. 2016. Learning deep features for discriminative localization（学习用于判别定位的深度特征）. In IEEE Conference on Computer Vision and Pattern Recognition. 2921-2929.
[49] Xiaowu Zou, Zidong Wang, Qi Li, 和 Weiguo Sheng. 2019. Integration of residual network and convolutional neural network along with various activation functions and global pooling for time series classification（残差网络和卷积神经网络的集成，以及各种激活函数和全局池化，用于时间序列分类）. Neurocomputing 367 (2019), 39-45.
[50] Yuhong Li, Xiaofan Zhang, 和 Deming Chen. 2018. CSRNet: Dilated convolutional neural networks for understanding the highly congested scenes（CSRNet：用于理解高度拥挤场景的扩张卷积神经网络）. In IEEE Conference on Computer Vision and Pattern Recognition. 1091-1100.
[51] Omolbanin Yazdanbakhsh 和 Scott Dick. 2019. Multivariate time series classification using dilated convolutional neural network（使用扩张卷积神经网络的多元时间序列分类）. arXiv preprint:1905.01697 (2019).


[52] Seyed Navid Mohammadi Foumani, Chang Wei Tan, and Mahsa Salehi. 2021. 用于多元时间序列分类的Disjoint-CNN。见2021年国际数据挖掘研讨会（ICDMW'21）。IEEE, 760-769.
[53] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. 2018. MobileNet-V2：倒残差和线性瓶颈（Inverted residuals and linear bottlenecks）。见IEEE计算机视觉与模式识别会议。4510-4520.
[54] Zhiguang Wang and Tim Oates. 2015. 将时间序列编码为图像，用于使用平铺卷积神经网络进行视觉检查和分类。见第29届AAAI人工智能会议研讨会。
[55] Nima Hatami, Yann Gavet, and Johan Debayle. 2018. 使用深度卷积神经网络对时间序列图像进行分类。见第10届国际机器视觉会议（ICMV'17），第10696卷。SPIE, 242-249.

[56] Saeed Karimi-Bidhendi, Faramarz Munshi, and Ashfaq Munshi. 2018. 单变量和多变量时间序列的可扩展分类。见2018年IEEE国际大数据会议（Big Data'18）。IEEE, 1598-1605.
[57] Yuxuan Zhao and Zhongmin Cai. 2019. 通过深度神经网络图像分类对多元时间序列进行分类。见2019年第二届中国认知计算与混合智能研讨会（CCHI'19）。IEEE, 93-98.
[58] Chao-Lung Yang, Zhi-Xuan Chen, and Chen-Yi Yang. 2019. 通过将多元时间序列编码为二维彩色图像，使用卷积神经网络进行传感器分类。Sensors 20, 1 (2019), 168.
[59] J-P. Eckmann S. Oliffson Kamphorst, D. Ruelle, et al. 1987. 动力系统的复发图（Recurrence plots）。Europhysics Letters 4, 9 (1987), 17.
[60] Christian Szegedy, Vincent Vanhoucke, Sergey Ioffe, Jon Shlens, and Zbigniew Wojna. 2016. 重新思考用于计算机视觉的Inception架构。见IEEE计算机视觉与模式识别会议。2818-2826.
[61] Wei Chen and Ke Shi. 2019. 一种使用相对位置矩阵（Relative Position Matrix）和卷积神经网络的时间序列分类深度学习框架。Neurocomputing 359 (2019), 384-394.
[62] Zhicheng Cui, Wenlin Chen, and Yixin Chen. 2016. 用于时间序列分类的多尺度卷积神经网络（Multi-scale convolutional neural networks）。arXiv preprint:1603.06995 (2016).
[63] Arthur Le Guennec, Simon Malinowski, and Romain Tavenard. 2016. 使用卷积神经网络的时间序列分类的数据增强（Data augmentation）。见ECML/PKDD关于时间数据高级分析和学习的研讨会。
[64] Chien-Liang Liu, Wen-Hoar Hsaio, and Yao-Chung Tu. 2018. 基于多元卷积神经网络的时间序列分类。IEEE Transactions on Industrial Electronics 66, 6 (2018), 4788-4797.
[65] Anthony Brunel, Johanna Pasquet, Jérôme Pasquet, Nancy Rodriguez, Frédéric Comby, Dominique Fouchez, and Marc Chaumont. 2019. 适用于时间序列的CNN，用于超新星的分类。Electronic Imaging 2019, 14 (2019), $90-1$.
[66] Jingyu Sun, Susumu Takeuchi, and Ikuo Yamasaki. 2021. 具有交叉分支注意力（cross branch attention）的原型Inception网络（Prototypical inception network），用于时间序列分类。见2021年国际神经网络联合会议（IJCNN'21）。IEEE, 1-7.
[67] Saidrasul Usmankhujaev, Bunyodbek Ibrokhimov, Shokhrukh Baydadaev, and Jangwoo Kwon. 2021. 基于InceptionFCN的时间序列分类。Sensors 22, 1 (2021), 157.
[68] Xueyuan Gong, Yain-Whar Si, Yongqi Tian, Cong Lin, Xinyuan Zhang, and Xiaoxiang Liu. 2022. KDCTime：在InceptionTime上使用校准的知识蒸馏（Knowledge distillation），用于时间序列分类。Information Science 613 (2022), $184-203$.
[69] Ali Ismail-Fawaz, Maxime Devanne, Stefano Berretti, Jonathan Weber, and Germain Forestier. 2023. LITE：用于时间序列分类的轻量级Inception与Boosting技术（Light inception with boosTing tEchniques）。见2023年IEEE第十届国际数据科学与高级分析会议（DSAA'23）。IEEE, 1-10.
[70] Christian Szegedy, Wei Liu, Yangqing Jia, Pierre Sermanet, Scott Reed, Dragomir Anguelov, Dumitru Erhan, Vincent Vanhoucke, and Andrew Rabinovich. 2015. 通过卷积加深。见IEEE计算机视觉与模式识别会议。1-9.
[71] Christian Szegedy, Sergey Ioffe, Vincent Vanhoucke, and Alexander A. Alemi. 2017. Inception-v4，Inception-ResNet以及残差连接（residual connections）对学习的影响。见第31届AAAI人工智能会议。
[72] Mutegeki Ronald, Alwin Poulose, and Dong Seog Han. 2021. iSPLInception：用于人类活动识别的Inception-ResNet深度学习架构。IEEE Access 9 (2021), 68985-69001.
[73] Ali Ismail-Fawaz, Maxime Devanne, Jonathan Weber, and Germain Forestier. 2022. 使用新手工卷积滤波器（hand-crafted convolution filters）的深度学习用于时间序列分类。见2022年IEEE国际大数据会议（Big Data'22）。IEEE, 972-981.
[74] Michael Hüsken and Peter Stagge. 2003. 用于时间序列分类的循环神经网络（Recurrent neural networks）。Neurocomputing 50 (2003), 223-235.
[75] Don Dennis, Durmus Alp Emre Acar, Vikram Mandikal, Vinu Sankar Sadasivan, Venkatesh Saligrama, Harsha Vardhan Simhadri, and Prateek Jain. 2019. 浅层RNN（Shallow RNN）：在资源受限设备上准确的时间序列分类。神经信息处理系统进展32 (2019), 11 pages.
[76] Santiago Fernández, Alex Graves, and Jürgen Schmidhuber. 2007. 使用分层循环神经网络（hierarchical recurrent neural networks）在结构化域中进行序列标记（Sequence labelling）。见第20届国际人工智能联合会议（IJCAI'07）。
[77] Michiel Hermans and Benjamin Schrauwen. 2013. 训练和分析深度循环神经网络。神经信息处理系统进展26 (2013), 190-198.
[78] Razvan Pascanu, Tomas Mikolov, and Yoshua Bengio. 2013. 关于训练循环神经网络的难度。见国际机器学习会议。PMLR, 1310-1318.
[79] Sepp Hochreiter and Jürgen Schmidhuber. 1997. 长短期记忆（Long short-term memory）。Neural Computation 9, 8 (1997), $1735-1780$.
[80] Junyoung Chung, Caglar Gulcehre, KyungHyun Cho, and Yoshua Bengio. 2014. 门控循环神经网络（gated recurrent neural networks）在序列建模上的实证评估。arXiv preprint:1412.3555 (2014).

[81] Ilya Sutskever, Oriol Vinyals, and Quoc V. Le. 2014. 基于神经网络的序列到序列学习（Sequence to sequence learning）。神经信息处理系统进展27 (2014), 3104-3112.
[82] Jeffrey Donahue, Lisa Anne Hendricks, Sergio Guadarrama, Marcus Rohrbach, Subhashini Venugopalan, Kate Saenko, and Trevor Darrell. 2015. 用于视觉识别和描述的长期循环卷积神经网络（Long-term recurrent convolutional networks）。见IEEE计算机视觉与模式识别会议。2625-2634.
[83] Andrej Karpathy and Li Fei-Fei. 2015. 用于生成图像描述的深度视觉语义对齐（Deep visual-semantic alignments）。见IEEE计算机视觉与模式识别会议。3128-3137.
[84] Yujin Tang, Jianfeng Xu, Kazunori Matsumoto, and Chihiro Ono. 2016. 用于时间序列分类的具有注意力机制的序列到序列模型（Sequence-to-sequence model with attention）。见2016年IEEE第16届国际数据挖掘研讨会（ICDMW'16）。IEEE, $503-510$.
[85] Pankaj Malhotra, Vishnu TV, Lovekesh Vig, Puneet Agarwal, and Gautam Shroff. 2017. TimeNet：用于时间序列分类的预训练深度循环神经网络（Pre-trained deep recurrent neural network）。arXiv preprint:1706.08838 (2017).
[86] Fazle Karim, Somshubra Majumdar, Houshang Darabi, and Samuel Harford. 2019. 用于时间序列分类的多元LSTM-FCNs（Multivariate LSTM-FCNs）。Neural Networks 116 (2019), 237-245.


[87] Xuchao Zhang, Yifeng Gao, Jessica Lin, and Chang-Tien Lu. 2020. Tapnet：基于注意力原型网络的多变量时间序列分类 (Multivariate time series classification with attentional prototypical network). In AAAI Conference on Artificial Intelligence, Vol. 34. 6845-6852.
[88] Jingwei Zuo, Karine Zeitouni, and Yehia Taher. 2021. SMATE：多变量时间序列上的半监督时空表示学习 (Semi-supervised spatio-temporal representation learning on multivariate time series). In 2021 IEEE International Conference on Data Mining (ICDM'21). IEEE, 1565-1570.
[89] Fazle Karim, Somshubra Majumdar, Houshang Darabi, and Shun Chen. 2017. 用于时间序列分类的LSTM全卷积网络 (LSTM fully convolutional networks for time series classification). IEEE Access 6 (2017), 1662-1669.
[90] Sangdi Lin and George C. Runger. 2017. GCRNN：组约束卷积循环神经网络 (Group-constrained convolutional recurrent neural network). IEEE Transactions on Neural Networks and Learning Systems 29, 10 (2017), 4709-4718.
[91] Ronald Mutegeki and Dong Seog Han. 2020. 一种基于CNN-LSTM的人类活动识别方法 (A CNN-LSTM approach to human activity recognition). In 2020 IEEE International Conference on Computational Intelligence and Communications Technologies. (ICAIIC'20). IEEE, $362-366$.
[92] Razvan Pascanu, Tomas Mikolov, and Yoshua Bengio. 2012. 理解梯度爆炸问题 (Understanding the exploding gradient problem). CoRR, abs/1211.5063 2, 417 (2012), 1.
[93] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N. Gomez, Łukasz Kaiser, and Illia Polosukhin. 2017. Attention is all you need. Advances in Neural Information Processing Systems 30 (2017).
[94] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. 2019. BERT：用于语言理解的深度双向Transformer的预训练 (Pre-training of deep bidirectional transformers for language understanding). In Proceedings of NAACL-HLT 2019, Vol. 1. Association for Computational Linguistics, Stroudsburg, PA, USA, 4171-4186. DOI : http://dx.doi.org/10.18653/v1/N19-1423 arXiv:1810.04805
[95] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. 2020. 一张图片胜过16x16个单词：用于大规模图像识别的Transformer (An image is worth 16x16 words: Transformers for image recognition at scale). arXiv preprint:2010.11929 (2020).
[96] Shiyang Li, Xiaoyong Jin, Yao Xuan, Xiyou Zhou, Wenhu Chen, Yu-Xiang Wang, and Xifeng Yan. 2019. 增强局部性并打破Transformer在时间序列预测上的内存瓶颈 (Enhancing the locality and breaking the memory bottleneck of transformer on time series forecasting). Advances in Neural Information Processing Systems 32 (2019).
[97] Haoyi Zhou, Shanghang Zhang, Jieqi Peng, Shuai Zhang, Jianxin Li, Hui Xiong, and Wancai Zhang. 2021. Informer：超越高效Transformer的长序列时间序列预测 (Beyond efficient transformer for long sequence time-series forecasting). In Proceedings of AAAI.
[98] Demetres Kostas, Stephane Aroca-Ouellette, and Frank Rudzicz. 2021. BENDR：使用Transformer和对比自监督学习任务从大量脑电图数据中学习 (Using transformers and a contrastive self-supervised learning task to learn from massive amounts of EEG data). Frontiers in Human Neuroscience 15 (2021).
[99] Ye Yuan, Guangxu Xun, Fenglong Ma, Yaqing Wang, Nan Du, Kebin Jia, Lu Su, and Aidong Zhang. 2018. Muvan：用于多变量时间数据的多视角注意力网络 (A multi-view attention network for multivariate temporal data). In 2018 IEEE International Conference on Data Mining (ICDM'18). IEEE, 717-726.
[100] Tsung-Yu Hsieh, Suhang Wang, Yiwei Sun, and Vasant Honavar. 2021. 可解释的多变量时间序列分类：一种深度神经网络，学习关注重要变量以及时间间隔 (Explainable multivariate time series classification: A deep neural network which learns to attend to important variables as well as time intervals). In 14th ACM International Conference on Web Search and Data Mining. 607-615.
[101] Wei Chen and Ke Shi. 2021. 用于时间序列分类的多尺度注意力卷积神经网络 (Multi-scale attention convolutional neural network for time series classification). Neural Networks 136 (2021), 126-140.
[102] Ye Yuan, Guangxu Xun, Fenglong Ma, Qiuling Suo, Hongfei Xue, Kebin Jia, and Aidong Zhang. 2018. 一种新颖的通道感知注意力框架，用于通过多视角深度学习进行多通道脑电癫痫检测 (A novel channelaware attention framework for multi-channel EEG seizure detection via multi-view deep learning). In 2018 IEEE EMBS International Conference on Biomedical \& Health Informatics (BHI'18). IEEE, 206-209.
[103] Yuxuan Liang, Songyu Ke, Junbo Zhang, Xiuwen Yi, and Yu Zheng. 2018. Geoman：用于地理感知时间序列预测的多层注意力网络 (Multi-level attention networks for geo-sensory time series prediction). In IJCAI, Vol. 2018. 3428-3434.
[104] Jun Hu and Wendong Zheng. 2020. 用于多变量时间序列预测的多阶段注意力网络 (Multistage attention network for multivariate time series prediction). Neurocomputing 383 (2020), 122-137.

[105] Xu Cheng, Peihua Han, Guoyuan Li, Shengyong Chen, and Houxiang Zhang. 2020. 一种用于多变量时间序列分类的卷积网络中的新型通道和时间注意力机制 (A novel channel and temporal-wise attention in convolutional networks for multivariate time series classification). IEEE Access 8 (2020), $212247-212257$.
[106] Zhiwen Xiao, Xin Xu, Huanlai Xing, Shouxi Luo, Penglin Dai, and Dawei Zhan. 2021. RTFN：一种用于时间序列分类的鲁棒时间特征网络 (A robust temporal feature network for time series classification). Information Sciences 571 (2021), 65-86.
[107] Jingyuan Wang, Chen Yang, Xiaohan Jiang, and Junjie Wu. 2023. WHEN：一种用于异构时间序列分析的Wavelet-DTW混合注意力网络 (A Wavelet-DTW hybrid attention network for heterogeneous time series analysis). In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining. 2361-2373.
[108] Max Jaderberg, Karen Simonyan, Andrew Zisserman, et al. 2015. 空间Transformer网络 (Spatial transformer networks). Advances in Neural Information Processing Systems 28 (2015), 2017-2025.
[109] Sanghyun Woo, Jongchan Park, Joon-Young Lee, and In So Kweon. 2018. CBAM：卷积块注意力模块 (Convolutional block attention module). In European Conference on Computer Vision. 3-19.
[110] Jie Hu, Li Shen, and Gang Sun. 2018. Squeeze-and-excitation网络 (Squeeze-and-excitation networks). In IEEE Conference on Computer Vision and Pattern Recognition. 7132-7141.
[111] Tian Wang, Zhaoying Liu, Ting Zhang, and Yujian Li. 2021. 基于多尺度动态卷积特征和距离特征的时间序列分类 (Time series classification based on multi-scale dynamic convolutional features and distance features). In 2021 2nd Asia Symposium on Signal Processing (ASSP'21). IEEE, $239-246$.
[112] Huan Song, Deepta Rajan, Jayaraman Thiagarajan, and Andreas Spanias. 2018. 注意并诊断：使用注意力模型进行临床时间序列分析 (Attend and diagnose: Clinical time series analysis using attention models). In AAAI Conference on Artificial Intelligence, Vol. 32.
[113] Can-can Jin and Xi Chen. 2021. 一种结合时频专家知识和改进的Transformer网络的端到端框架，用于振动信号分类 (An end-to-end framework combining time-frequency expert knowledge and modified Transformer networks for vibration signal classification). Expert Systems with Applications 171 (2021), 114570.
[114] Carl Edward Rasmussen. 2004. 机器学习中的高斯过程 (Gaussian Processes in Machine Learning). Springer, Berlin, 63-71.
[115] Tarek Allam Jr. and Jason D. McEwen. 2021. 关注天文瞬变现象：使用时间序列Transformer进行光度分类 (Paying attention to astronomical transients: Photometric classification with the time-series transformer). arXiv preprint:2105.06178 (2021).
[116] Minghao Liu, Shengqi Ren, Siyuan Ma, Jiahui Jiao, Yizhou Chen, Zhiguang Wang, and Wei Song. 2021. 用于多变量时间序列分类的门控Transformer网络 (Gated transformer networks for multivariate time series classification). arXiv preprint:2103.14438 (2021).
[117] Bowen Zhao, Huanlai Xing, Xinhan Wang, Fuhong Song, and Zhiwen Xiao. 2022. 重新思考时间序列分类中的注意力机制 (Rethinking attention mechanism in time series classification). arXiv preprint:2207.07564 (2022).
[118] Yankun Ren, Longfei Li, Xinxing Yang, and Jun Zhou. 2022. AutoTransformer：用于时间序列分类的自动Transformer架构设计 (Automatic transformer architecture design for time series classification). In Pacific-Asia Conference on Knowledge Discovery and Data Mining. Springer, $143-155$.
[119] Ming Jin, Huan Yee Koh, Qingsong Wen, Daniele Zambon, Cesare Alippi, Geoffrey I. Webb, Irwin King, and Shirui Pan. 2023. 图神经网络在时间序列上的应用综述：预测、分类、插补和异常检测 (A survey on graph neural networks for time series: Forecasting, classification, imputation, and anomaly detection). arXiv 14, 8 (July 2023), 1-27. arXiv:2307.03759 http://arxiv.org/abs/2307.03759


[120] Zonghan Wu, Shirui Pan, Fengwen Chen, Guodong Long, Chengqi Zhang, 和 Philip S. Yu. 2021. A comprehensive survey on graph neural networks (图神经网络). IEEE Transactions on Neural Networks and Learning Systems 32, 1 (Jan. 2021), 4-24. DOI : http://dx.doi.org/10.1109/TNNLS.2020.2978386 arXiv:1901.00596
[121] Franco Scarselli, Marco Gori, Ah Chung Tsoi, Markus Hagenbuchner, 和 Gabriele Monfardini. 2009. The graph neural network model (图神经网络模型). IEEE Transactions on Neural Networks 20, 1 (Jan. 2009), 61-80. DOI : http://dx.doi.org/10.1109/ TNN. 2008.2005605
[122] Wenjie Xi, Arnav Jain, Li Zhang, 和 Jessica Lin. 2023. LB-SimTSC: An efficient similarity-aware graph neural network for semi-supervised time series classification (半监督时间序列分类). arXiv (Jan. 2023). arXiv:2301.04838
[123] Huaiyuan Liu, Xianzhang Liu, Donghua Yang, Zhiyu Liang, Hongzhi Wang, Yong Cui, 和 Jun Gu. 2023. TodyNet: Temporal dynamic graph neural network for multivariate time series classification (多变量时间序列分类). ArXiv abs/2304.05078 (2023). https://api.semanticscholar.org/CorpusID:258059979
[124] Stefan Bloemheuvel, Jurgen van den Hoogen, Dario Jozinović, Alberto Michelini, 和 Martin Atzmueller. 2023. Graph neural networks for multivariate time series regression with application to seismic data (应用于地震数据的多变量时间序列回归). International Journal of Data Science and Analytics 16, 3 (Sept. 2023), 317-332. DOI : http://dx.doi.org/10.1007/s41060-022-00349-6 arXiv:2201.00818
[125] Ziqiang Cheng, Yang Yang, Shuo Jiang, Wenjie Hu, Zhangchi Ying, Ziwei Chai, 和 Chunping Wang. 2021. Time2Graph+: Bridging time series and graph representation learning via multiple attentions (通过多重注意力桥接时间序列和图表示学习). IEEE Transactions on Knowledge and Data Engineering 35, 2 (2021), 1-1. DOI : http://dx.doi.org/10.1109/TKDE.2021.3094908
[126] Ian C. Covert, Balu Krishnan, Imad Najm, Jiening Zhan, Matthew Shore, John Hixson, 和 Ming Jack Po. 2019. Temporal graph convolutional networks for automatic seizure detection (自动癫痫检测). In Machine Learning for Healthcare Conference. PMLR, $160-180$.
[127] Tengfei Song, Wenming Zheng, Peng Song, 和 Zhen Cui. 2020. EEG emotion recognition using dynamical graph convolutional neural networks (使用动态图卷积神经网络的脑电图情感识别). IEEE Transactions on Affective Computing 11, 3 (July 2020), 532-541. DOI : http://dx. doi.org/10.1109/TAFFC.2018.2817622

[128] Ziyu Jia, Youfang Lin, Jing Wang, Ronghao Zhou, Xiaojun Ning, Yuanlai He, 和 Yaoshuai Zhao. 2020. GraphSleepNet: Adaptive spatial-temporal graph convolutional networks for sleep stage classification (睡眠阶段分类). In IJCAI. 1324-1330.
[129] Zhengjing Ma, Gang Mei, Edoardo Prezioso, Zhongjian Zhang, 和 Nengxiong Xu. 2021. A deep learning approach using graph convolutional networks for slope deformation prediction based on time-series displacement data (基于时间序列位移数据的斜坡变形预测). Neural Computing and Applications 33, 21 (2021), 14441-14457.
[130] Tianfu Li, Zhibin Zhao, Chuang Sun, Ruqiang Yan, 和 Xuefeng Chen. 2020. Multireceptive field graph convolutional networks for machine fault diagnosis (机器故障诊断). IEEE Transactions on Industrial Electronics 68, 12 (2020), 12739-12749.
[131] D. Nhu, M. Janmohamed, P. Perucca, A. Gilligan, P. Kwan, T. O'Brien, C. W. Tan, 和 L. Kuhlmann. 2021. Graph convolutional network for generalized epileptiform abnormality detection on EEG (脑电图上的广义癫痫样异常检测). In 2021 IEEE Signal Processing in Medicine and Biology Symposium (SPMB'21). IEEE, 1-6.
[132] Siyi Tang, Jared A. Dunnmon, Khaled Saab, Xuan Zhang, Qianying Huang, Florian Dubost, Daniel L. Rubin, 和 Christopher Lee-Messer. 2021. Self-supervised graph neural networks for improved electroencephalographic seizure analysis (脑电图癫痫分析). In 10th International Conference on Learning Representations (ICLR'22), 1-23. arXiv:2104.08336
[133] Xiang Zhang, Marko Zeman, Theodoros Tsiligkaridis, 和 Marinka Zitnik. 2021. Graph-guided network for irregularly sampled multivariate time series (不规则采样多变量时间序列). In 10th International Conference on Learning Representations (ICLR'22), 1-21. arXiv:2110.05357
[134] Alessandro Michele Censi, Dino Ienco, Yawogan Jean Eudes Gbodjo, Ruggero Gaetano Pensa, Roberto Interdonato, 和 Raffaele Gaetano. 2021. Attentive spatial temporal graph CNN for land cover mapping from multi temporal remote sensing data (多时相遥感数据的土地覆盖制图). IEEE Access 9 (2021), 23070-23082. DOI : http://dx.doi.org/10.1109/ACCESS.2021.3055554
[135] Tiago Azevedo, Alexander Campbell, Rafael Romero-Garcia, Luca Passamonti, Richard A. I. Bethlehem, Pietro Liò, 和 Nicola Toschi. 2022. A deep graph neural network architecture for modelling spatio-temporal dynamics in resting-state functional MRI data (静息态功能磁共振成像数据中的时空动力学建模). Medical Image Analysis 79 (July 2022), 102471. DOI : http://dx.doi.org/10.1016/j. media.2022.102471
[136] Ziheng Duan, Haoyan Xu, Yueyang Wang, Yida Huang, Anni Ren, Zhongbin Xu, Yizhou Sun, 和 Wei Wang. 2022. Multivariate time-series classification with hierarchical variational graph pooling (分层变分图池化的多变量时间序列分类). Neural Networks 154 (Oct. 2022), 481-490. DOI : http://dx.doi.org/10.1016/j.neunet.2022.07.032 arXiv:2010.05649
[137] Daochen Zha, Kwei-herng Lai, Kaixiong Zhou, 和 Xia Hu. 2022. Towards similarity-aware time-series classification (相似性感知的时间序列分类). In Proceedings of the 2022 SIAM International Conference on Data Mining (SDM'22). 199-207. DOI : http://dx.doi.org/ 10.1137/1.9781611977172.23
[138] Lukasz Tulczyjew, Michal Kawulok, Nicolas Longepe, Bertrand Le Saux, 和 Jakub Nalepa. 2022. Graph neural networks extract high-resolution cultivated land maps from sentinel-2 image series (从Sentinel-2图像序列中提取高分辨率耕地地图). IEEE Geoscience and Remote Sensing Letters 19 (2022), 1-5. DOI : http://dx.doi.org/10.1109/LGRS.2022.3185407
[139] Le Sun, Chenyang Li, Bo Liu, 和 Yanchun Zhang. 2023. Class-driven graph attention network for multi-label time series classification in mobile health digital twins (移动健康数字孪生中的多标签时间序列分类). IEEE Journal on Selected Areas in Communications 41, 10 (2023), 3267-3278. DOI : http://dx.doi.org/10.1109/JSAC.2023.3310064
[140] Corentin Dufourg, Charlotte Pelletier, Stéphane May, 和 Sébastien Lefèvre. 2023. Graph dynamic earth net: Spatiotemporal graph benchmark for satellite image time series (卫星图像时间序列的时空图基准). In 2023 IEEE International Geoscience and Remote Sensing Symposium (IGARSS'23). IEEE, 7164-7167. DOI : http://dx.doi.org/10.1109/IGARSS52108.2023.10281458
[141] Eamonn Keogh 和 Chotirat Ann Ratanamahatana. 2005. Exact indexing of dynamic time warping (动态时间规整). Knowledge Information Systems 7, 3 (2005), 358-386. DOI : http://dx.doi.org/10.1007/s10115-004-0154-9
[142] Thomas N. Kipf 和 Max Welling. 2016. Semi-supervised classification with graph convolutional networks (图卷积网络的半监督分类). In 5th International Conference on Learning Representations (ICLR'17) - Conference Track Proceedings. 1-14. arXiv:1609.02907
[143] Ling Yang 和 Shenda Hong. 2022. Unsupervised time-series representation learning with iterative bilinear temporalspectral fusion (迭代双线性时频谱融合的无监督时间序列表示学习). In ICML. 25038-25054.
[144] Aapo Hyvarinen 和 Hiroshi Morioka. 2016. Unsupervised feature extraction by time-contrastive learning and nonlinear ICA (通过时间对比学习和非线性ICA的无监督特征提取). Advances in Neural Information Processing Systems 29 (2016), 3772-3780.
[145] Jean-Yves Franceschi, Aymeric Dieuleveut, 和 Martin Jaggi. 2019. Unsupervised scalable representation learning for multivariate time series (多变量时间序列的无监督可扩展表示学习). NeurIPS 418, 32 (2019), 12 pages.
[146] Sana Tonekaboni, Danny Eytan, 和 Anna Goldenberg. 2021. Unsupervised representation learning for time series with temporal neighborhood coding (时间邻域编码的时间序列无监督表示学习). International Conference on Learning Representations. https://openreview.net/ forum?id=8qDwejCuCN


[147] Kristoffer Wickstrøm, Michael Kampffmeyer, Karl Øyvind Mikalsen, 和 Robert Jenssen. 2022. Mixing up contrastive learning: Self-supervised representation learning for time series（混合对比学习：时间序列的自监督表征学习）. Pattern Recognition Letters 155 (2022), 54-61.
[148] Xinyu Yang, Zhenguo Zhang, 和 Rongyi Cui. 2022. TimeCLR: A self-supervised contrastive learning framework for univariate time series representation（TimeCLR：用于单变量时间序列表示的自监督对比学习框架）. Knowledge-based Systems 245 (2022), 108606.
[149] Xiang Zhang, Ziyuan Zhao, Theodoros Tsiligkaridis, 和 Marinka Zitnik. 2022. Self-supervised contrastive pretraining for time series via time-frequency consistency（通过时频一致性的时间序列自监督对比预训练）. In Proceedings of Neural Information Processing Systems (NeurIPS'22).

[150] Qianwen Meng, Hangwei Qian, Yong Liu, Lizhen Cui, Yonghui Xu, 和 Zhiqi Shen. 2023. MHCCL: Masked hierarchical cluster-wise contrastive learning for multivariate time series（MHCCL：用于多元时间序列的掩码分层聚类式对比学习）. In Proceedings of the AAAI Conference on Artificial Intelligence, Vol. 37. 9153-9161.
[151] Ranak Roy Chowdhury, Xiyuan Zhang, Jingbo Shang, Rajesh K. Gupta, 和 Dezhi Hong. 2022. TARNet: Task-aware reconstruction for time-series transformer（TARNet：用于时间序列 Transformer 的任务感知重建）. In 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining. $14-18$.
[152] Mingyue Cheng, Qi Liu, Zhiding Liu, Hao Zhang, Rujiao Zhang, 和 Enhong Chen. 2023. TimeMAE: Self-supervised representations of time series with decoupled masked autoencoders（TimeMAE：具有解耦掩码自编码器的时间序列自监督表示）. arXiv preprint arXiv:2303.00320 (2023).
[153] Wenrui Zhang, Ling Yang, Shijia Geng, 和 Shenda Hong. 2023. Self-supervised time series representation learning via cross reconstruction transformer（通过交叉重建 Transformer 的自监督时间序列表征学习）. IEEE Transactions on Neural Networks and Learning Systems (2023), 1-10.
[154] Ali Ismail-Fawaz, Maxime Devanne, Stefano Berretti, Jonathan Weber, 和 Germain Forestier. 2023. Finding foundation models for time series classification with a PreText task（通过 PreText 任务寻找时间序列分类的基础模型）. arXiv preprint arXiv:2311.14534 (2023).
[155] Connor Shorten 和 Taghi M. Khoshgoftaar. 2019. A survey on image data augmentation for deep learning（深度学习的图像数据增强综述）. Journal of Big Data 6, 1 (2019), 1-48.
[156] Terry T. Um, Franz M. J. Pfister, Daniel Pichler, Satoshi Endo, Muriel Lang, Sandra Hirche, Urban Fietzek, 和 Dana Kulić. 2017. Data augmentation of wearable sensor data for Parkinson's disease monitoring using convolutional neural networks（使用卷积神经网络对可穿戴传感器数据进行数据增强，用于帕金森病监测）. In Proceedings of the 19th ACM International Conference on Multimodal Interaction. $216-220$.
[157] Khandakar M. Rashid 和 Joseph Louis. 2019. Window-warping: A time series data augmentation of IMU data for construction equipment activity identification（窗口扭曲：一种用于建筑设备活动识别的 IMU 数据的时间序列数据增强方法）. In Proceedings of the International Symposium on Automation and Robotics in Construction (ISARC'19), Vol. 36. IAARC Publications, 651-657.
[158] Brian Kenji Iwana 和 Seiichi Uchida. 2021. Time series data augmentation for neural networks by time warping with a discriminative teacher（通过具有判别教师的时间扭曲进行神经网络的时间序列数据增强）. In 2020 25th International Conference on Pattern Recognition (ICPR'21). IEEE, $3558-3565$.
[159] Thai-Son Nguyen, Sebastian Stueker, Jan Niehues, 和 Alex Waibel. 2020. Improving sequence-to-sequence speech recognition training with on-the-fly data augmentation（通过即时数据增强改进序列到序列语音识别训练）. In 2020 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP'20). IEEE, 7689-7693.
[160] Bhavik Vachhani, Chitralekha Bhat, 和 Sunil Kumar Kopparapu. 2018. Data augmentation using healthy speech for dysarthric speech recognition（使用健康语音进行数据增强，用于构音障碍语音识别）. In Interspeech. 471-475.
[161] Jingkun Gao, Xiaomin Song, Qingsong Wen, Pichao Wang, Liang Sun, 和 Huan Xu. 2020. RobustTAD: Robust Time Series Anomaly Detection via Decomposition and Convolutional Neural Networks（RobustTAD：通过分解和卷积神经网络的鲁棒时间序列异常检测）. arXiv:cs.LG/2002.09545 (2020).
[162] Zhicheng Cui, Wenlin Chen, 和 Yixin Chen. 2016. Multi-Scale Convolutional Neural Networks for Time Series Classification（用于时间序列分类的多尺度卷积神经网络）. arXiv:cs.CV/1603.06995 (2016).
[163] Arthur Le Guennec, Simon Malinowski, 和 Romain Tavenard. 2016. Data augmentation for time series classification using convolutional neural networks（使用卷积神经网络进行时间序列分类的数据增强）. In ECML/PKDD on Advanced Analytics and Learning on Temporal Data.
[164] Germain Forestier, François Petitjean, Hoang Anh Dau, Geoffrey I. Webb, 和 Eamonn Keogh. 2017. Generating synthetic time series to augment sparse datasets（生成合成时间序列以增强稀疏数据集）. In 2017 IEEE International Conference on Data Mining (ICDM'17). IEEE, $865-870$.
[165] Hassan Ismail Fawaz, Germain Forestier, Jonathan Weber, Lhassane Idoumghar, 和 Pierre-Alain Muller. 2018. Data Augmentation Using Synthetic Data for Time Series Classification with Deep Residual Networks（使用合成数据进行数据增强，用于基于深度残差网络的时间序列分类）. arXiv:cs.CV/1808.02455 (2018).
[166] Tsegamlak Terefe, Maxime Devanne, Jonathan Weber, Dereje Hailemariam, 和 Germain Forestier. 2020. Time series averaging using multi-tasking autoencoder（使用多任务自编码器的时间序列平均）. In 2020 IEEE 32nd International Conference on Tools with Artificial Intelligence (ICTAI'20). IEEE, 1065-1072.
[167] Brian Kenji Iwana 和 Seiichi Uchida. 2021. An empirical survey of data augmentation for time series classification with neural networks（使用神经网络进行时间序列分类的数据增强的实证调查）. Plos One 16, 7 (2021), e0254841.
[168] Gautier Pialla, Maxime Devanne, Jonathan Weber, Lhassane Idoumghar, 和 Germain Forestier. 2022. Data augmentation for time series classification with deep learning models（使用深度学习模型进行时间序列分类的数据增强）. In International Workshop on Advanced Analytics and Learning on Temporal Data. Springer, 117-132.
[169] Zijun Gao, Lingbo Li, 和 Tianhua Xu. 2023. Data augmentation for time-series classification: An extensive empirical study and comprehensive survey（时间序列分类的数据增强：一项广泛的实证研究和综合调查）. arXiv preprint arXiv:2310.10060 (2023).
[170] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, 和 Li Fei-Fei. 2009. Imagenet: A large-scale hierarchical image database（Imagenet：一个大规模的分层图像数据库）. In 2009 IEEE Conference on Computer Vision and Pattern Recognition. IEEE, 248-255.
[171] Hassan Ismail Fawaz, Germain Forestier, Jonathan Weber, Lhassane Idoumghar, 和 Pierre-Alain Muller. 2018. Transfer learning for time series classification（时间序列分类的迁移学习）. In 2018 IEEE International Conference on Big Data (Big Data'18). IEEE, $1367-1376$.

[172] Stephan Spiegel. 2016. Transfer learning for time series classification in dissimilarity spaces（相异空间中时间序列分类的迁移学习）. Proceedings of AALTD 78 (2016).
[173] Frédéric Li, Kimiaki Shirahama, Muhammad Adeel Nisar, Xinyu Huang, 和 Marcin Grzegorzek. 2020. Deep transfer learning for time series data based on sensor modality classification（基于传感器模态分类的时间序列数据深度迁移学习）. Sensors 20, 15 (2020), 4271.
[174] Yarden Rotem, Nathaniel Shimoni, Lior Rokach, 和 Bracha Shapira. 2022. Transfer learning for time series classification using synthetic data generation（使用合成数据生成的时间序列分类的迁移学习）. In International Symposium on Cyber Security, Cryptology, and Machine Learning. Springer, 232-246.
[175] Ayantha Senanayaka, Abdullah Al Mamun, Glenn Bond, Wenmeng Tian, Haifeng Wang, Sara Fuller, T. C. Falls, Shahram Rahimi, 和 Linkan Bian. 2022. Similarity-based multi-source transfer learning approach for time series classification（基于相似性的多源迁移学习方法用于时间序列分类）. International Journal of Prognostics and Health Management 13, 2 (2022).
[176] Kathan Kashiparekh, Jyoti Narwariya, Pankaj Malhotra, Lovekesh Vig, 和 Gautam Shroff. 2019. Convtimenet: A pre-trained deep convolutional neural network for time series classification（Convtimenet：用于时间序列分类的预训练深度卷积神经网络）. In 2019 International Joint Conference on Neural Networks (IJCNN'19). IEEE, 1-8.
[177] D. Merlin Praveena, D. Angelin Sarah, 和 S. Thomas George. 2022. Deep learning techniques for EEG signal applications-A review（脑电信号应用的深度学习技术——综述）. IETE Journal of Research 68, 4 (2022), 3030-3037. DOI : http://dx.doi.org/10.1080/03772063.2020. 1749143
[178] Xinwen Liu, Huan Wang, Zongjin Li, 和 Lang Qin. 2021. Deep learning in ECG diagnosis: A review（心电图诊断中的深度学习：综述）. Knowledgebased Systems 227 (2021), 107187. DOI : http://dx.doi.org/10.1016/j.knosyx.2021.107187


[179] Nur'atiah Zaini, Lee Woen Ean, Ali Najah Ahmed, 和 Marlinda Abdul Malek. 2022. 深度学习神经网络用于时间序列空气质量预测的系统性文献综述 (*A systematic literature review of deep learning neural network for time series air quality forecasting*). *Environmental Science and Pollution Research* 29, 4 (Jan. 2022), 4958-4990. DOI : http://dx.doi.org/10.1007/s11356-021-17442-1
[180] Bo Zhang, Yi Rong, Ruihan Yong, Dongming Qin, Maozhen Li, Guojian Zou, 和 Jianguo Pan. 2022. 深度学习用于空气污染物浓度预测：综述 (*Deep learning for air pollutant concentration prediction: A review*). *Atmospheric Environment* 290 (Dec. 2022), 119347. DOI : http: //dx.doi.org/10.1016/j.atmosenv.2022.119347
[181] Gyungmin Toh 和 Junhong Park. 2020. 基于深度学习的振动结构健康监测综述 (*Review of vibration-based structural health monitoring using deep learning*). *Applied Sciences* 10, 5 (2020), 1680. DOI : http://dx.doi.org/10.3390/app10051680
[182] Nikhil M. Thoppil, V. Vasu, 和 C. S. P. Rao. 2021. 使用时间序列数据的机械健康预测的深度学习算法：综述 (*Deep learning algorithms for machinery health prognostics using time-series data: A review*). *Journal of Vibration Engineering \& Technologies* 9, 6 (Sept. 2021), 1123-1145. DOI : http: //dx.doi.org/10.1007/s42417-021-00286-x
[183] Lei Ren, Zidi Jia, Yuanjun Laili, 和 Di Huang. 2023. 工业物联网（IIoT）中用于时间序列预测的深度学习：进展、挑战和前景 (*Deep learning for time-series prediction in IIoT: Progress, challenges, and prospects*). *IEEE Transactions on Neural Networks and Learning Systems* PP (2023), 1-20. DOI : http: //dx.doi.org/10.1109/TNNLS.2023.3291371
[184] Yassine Himeur, Khalida Ghanem, Abdullah Alsalemi, Faycal Bensaali, 和 Abbes Amira. 2021. 基于人工智能的建筑物能耗异常检测：综述、当前趋势和新视角 (*Artificial intelligence based anomaly detection of energy consumption in buildings: A review, current trends and new perspectives*). *Applied Energy* 287 (2021), 116601. DOI : http://dx.doi.org/10.1016/j.apenergy.2021.116601
[185] Dan Stowell. 2022. 基于深度学习的计算生物声学：综述和路线图 (*Computational bioacoustics with deep learning: A review and roadmap*). *PeerJ* 10 (March 2022), e13152. DOI : http://dx.doi.org/10.7717/peerj. 13152
[186] Neha Gupta, Suneet K. Gupta, Rajesh K. Pathak, Vanita Jain, Parisa Rashidi, 和 Jasjit S. Suri. 2022. 人工智能框架中的人类活动识别：叙述性综述 (*Human activity recognition in artificial intelligence framework: A narrative review*). *Artificial Intelligence Review* 55, 6 (Aug. 2022), 4755-4808. DOI : http://dx.doi.org/10.1007/s10462-021-10116-x
[187] E. Ramanujam, Thinagaran Perumal, 和 S. Padmavathi. 2021. 使用深度学习技术通过智能手机和可穿戴传感器进行的人类活动识别：综述 (*Human activity recognition with smartphone and wearable sensors using deep learning techniques: A review*). *IEEE Sensors Journal* 21, 12 (June 2021), 13029-13040. DOI : http://dx.doi.org/10.1109/JSEN.2021.3069927
[188] Jeffrey W. Lockhart, Tony Pulickal, 和 Gary M. Weiss. 2012. 移动活动识别的应用 (*Applications of mobile activity recognition*). In *2012 ACM Conference on Ubiquitous Computing* (UbiComp'12). ACM Press, New York, NY, USA, 1054. DOI : http://dx.doi. org/10.1145/2370216.2370441
[189] Emmanuel Munguia Tapia, Stephen S. Intille, 和 Kent Larson. 2004. 在家庭中使用简单且无处不在的传感器进行活动识别 (*Activity recognition in the home using simple and ubiquitous sensors*). In *Lecture Notes in Computer Science*. Vol. 3001. Springer, Berlin, 158-175. DOI : http://dx.doi. org/10.1007/978-3-540-24646-6_10
[190] Yu Kong 和 Yun Fu. 2022. 人类动作识别和预测：综述 (*Human action recognition and prediction: A survey*). *International Journal of Computer Vision* 130, 5 (May 2022), 1366-1401. DOI : http://dx.doi.org/10.1007/s11263-022-01594-9 arXiv:1806.11230
[191] Hong-Bo Zhang, Yi-Xiang Zhang, Bineng Zhong, Qing Lei, Lijie Yang, Ji-Xiang Du, 和 Duan-Sheng Chen. 2019. 基于视觉的人类动作识别方法的综合调查 (*A comprehensive survey of vision-based human action recognition methods*). *Sensors* 19, 5 (Feb. 2019), 1005. DOI : http: //dx.doi.org/10.3390/s19051005
[192] Francisco Ordóñez 和 Daniel Roggen. 2016. 用于多模态可穿戴活动识别的深度卷积和LSTM循环神经网络 (*Deep convolutional and LSTM recurrent neural networks for multimodal wearable activity recognition*). *Sensors* 16, 1 (Jan. 2016), 115. DOI : http://dx.doi.org/10.3390/s16010115

[193] Attila Reiss 和 Didier Stricker. 2012. 引入用于活动监控的新基准数据集 (*Introducing a new benchmarked dataset for activity monitoring*). In *16th International Symposium on Wearable Computers*. 108-109. DOI : http://dx.doi.org/10.1109/ISWC.2012.13
[194] Mi Zhang 和 Alexander A. Sawchuk. 2012. USC-HAD：用于使用可穿戴传感器进行无处不在的活动识别的日常活动数据集 (*USC-HAD: A daily activity dataset for ubiquitous activity recognition using wearable sensors*). In *2012 ACM Conference on Ubiquitous Computing* (UbiComp'12). ACM Press, New York, NY, USA, 1036. DOI : http://dx.doi.org/10.1145/2370216.2370438
[195] Daniel Roggen, Alberto Calatroni, Mirco Rossi, Thomas Holleczek, Kilian Förster, Gerhard Tröster, Paul Lukowicz, David Bannach, Gerald Pirkl, et al. 2010. 在高度丰富的网络传感器环境中收集复杂活动数据集 (*Collecting complex activity datasets in highly rich networked sensor environments*). In *7th International Conference on Networked Sensing Systems*. IEEE, 233-240.
[196] Timo Sztyler, Heiner Stuckenschmidt, 和 Wolfgang Petrich. 2017. 使用可穿戴设备进行位置感知活动识别 (*Position-aware activity recognition with wearable devices*). *Pervasive and Mobile Computing* 38 (July 2017), 281-295. DOI : http://dx.doi.org/10.1016/j.pmcj.2017.01.008
[197] Oscar D. Lara 和 Miguel A. Labrador. 2013. 使用可穿戴传感器进行人类活动识别的调查 (*A survey on human activity recognition using wearable sensors*). *IEEE Communications Surveys \& Tutorials* 15, 3 (2013), 1192-1209. DOI : http://dx.doi.org/10.1109/SURV.2012.110112.00192
[198] Fuqiang Gu, Mu-Huan Chung, Mark Chignell, Shahrokh Valaee, Baoding Zhou, 和 Xue Liu. 2022. 深度学习用于人类活动识别的调查 (*A survey on deep learning for human activity recognition*). *Computer Surveys* 54, 8 (Nov. 2022), 1-34. DOI : http://dx.doi.org/10.1145/ 3472290
[199] Nils Y. Hammerla, Shane Halloran, 和 Thomas Ploetz. 2016. 使用可穿戴设备的深度、卷积和循环模型用于人类活动识别 (*Deep, convolutional, and recurrent models for human activity recognition using wearables*). In *International Joint Conference on Artificial Intelligence* (IJCAI'16). 1533-1540. arXiv:1604.08880
[200] Ming Zeng, Le T. Nguyen, Bo Yu, Ole J. Mengshoel, Jiang Zhu, Pang Wu, 和 Joy Zhang. 2014. 用于使用移动传感器进行人类活动识别的卷积神经网络 (*Convolutional neural networks for human activity recognition using mobile sensors*). In *6th International Conference on Mobile Computing, Applications and Services*. ICST, 718-737. DOI : http://dx.doi.org/10.4108/icst.mobicase.2014.257786
[201] Wenchao Jiang 和 Zhaozheng Yin. 2015. 通过深度卷积神经网络使用可穿戴传感器进行人类活动识别 (*Human activity recognition using wearable sensors by deep convolutional neural networks*). In *23rd ACM International Conference on Multimedia*. ACM, New York, NY, USA, 1307-1310. DOI : http://dx.doi.org/10.1145/2733373.2806333
[202] Jian Bo Yang, Minh Nhut Nguyen, Phyo Phyo San, Xiao Li Li, 和 Shonali Krishnaswamy. 2015. 用于人类活动识别的多通道时间序列上的深度卷积神经网络 (*Deep convolutional neural networks on multichannel time series for human activity recognition*). In *International Joint Conference on Artificial Intelligence* (IJCAI '15), 3995-4001. DOI : http://dx.doi.org/10.5555/2832747.2832806
[203] Charissa Ann Ronao 和 Sung-Bae Cho. 2016. 使用深度学习神经网络通过智能手机传感器进行的人类活动识别 (*Human activity recognition with smartphone sensors using deep learning neural networks*). *Expert Systems with Applications* 59 (Oct. 2016), 235-244. DOI : http://dx.doi.org/10.1016/j. eswa.2016.04.032
[204] Yu Guan 和 Thomas Plötz. 2017. 用于使用可穿戴设备进行活动识别的深度LSTM学习器集成 (*Ensembles of deep LSTM learners for activity recognition using wearables*). *ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies* 1, 2 (June 2017), 1-28. DOI : http://dx.doi.org/10.1145/ 3090076 arXiv:1703.09370
[205] Song-Mi Lee, Sang Min Yoon, 和 Heeryon Cho. 2017. 使用卷积神经网络从加速度计数据进行的人类活动识别 (*Human activity recognition from accelerometer data using Convolutional Neural Network*). In *2017 IEEE International Conference on Big Data and Smart Computing* (BigComp'17), Vol. 83. IEEE, 131-134. DOI : http://dx.doi.org/10.1109/BIGCOMP.2017.7881728
[206] Abdulmajid Murad 和 Jae-Young Pyun. 2017. 用于人类活动识别的深度循环神经网络 (*Deep recurrent neural networks for human activity recognition*). *Sensors* 17, 11 (Nov. 2017), 2556. DOI : http://dx.doi.org/10.3390/s17112556


[207] Andrey Ignatov. 2018. 使用卷积神经网络从加速度计数据进行实时人体活动识别. *应用软计算* 62 (2018年1月), 915-922. DOI : http://dx.doi.org/10.1016/j.asoc.2017.09.027
[208] Fernando Moya Rueda, René Grzeszick, Gernot Fink, Sascha Feldhorst, 和 Michael ten Hompel. 2018. 使用穿戴式传感器进行人体活动识别的卷积神经网络. *Informatics* 5, 2 (2018年5月), 26. DOI : http: //dx.doi.org/10.3390/informatics5020026
[209] Rui Yao, Guosheng Lin, Qinfeng Shi, 和 Damith C. Ranasinghe. 2018. 使用全卷积网络从可穿戴设备高效密集地标记人体活动序列. *模式识别* 78 (2018年6月), 252-266. DOI : http: //dx.doi.org/10.1016/j.patcog.2017.12.024 arXiv:1702.06212
[210] Ming Zeng, Haoxiang Gao, Tong Yu, Ole J. Mengshoel, Helge Langseth, Ian Lane, 和 Xiaobing Liu. 2018. 通过连续注意力理解和改进用于人体活动识别的循环神经网络. 在 *ACM国际可穿戴计算机研讨会* 中. 纽约, NY, 美国, 56-63. DOI : http://dx.doi.org/10.1145/3267242.3267286 arXiv:1810.04038
[211] Haojie Ma, Wenzhong Li, Xiao Zhang, Songcheng Gao, 和 Sanglu Lu. 2019. AttnSense: 用于多模态人体活动识别的多级注意力机制. 在 *第28届国际人工智能联合会议* 中. 3109-3115. DOI : http://dx.doi.org/10.24963/ijcai.2019/431
[212] Cheng Xu, Duo Chai, Jie He, Xiaotong Zhang, 和 Shihong Duan. 2019. InnoHAR: 用于复杂人体活动识别的深度神经网络. *IEEE Access* 7 (2019), 9893-9902. DOI : http://dx.doi.org/10.1109/ACCESS.2018.2890675
[213] Haoxi Zhang, Zhiwen Xiao, Juan Wang, Fei Li, 和 Edward Szczerbicki. 2020. 一种新颖的物联网感知人体活动识别 (HAR) 方法，使用多头卷积注意力. *IEEE物联网期刊* 7, 2 (2020年2月), 1072-1080. DOI : http://dx.doi.org/10.1109/JIOT.2019.2949715

ACM Comput. Surv., Vol. 56, No. 9, Article 217. Publication date: April 2024.

[214] Sravan Kumar Challa, Akhilesh Kumar, 和 Vijay Bhaskar Semwal. 2021. 一种用于使用可穿戴传感器数据进行人体活动识别的多分支CNN-BiLSTM模型. *The Visual Computer* 38, 0123456789 (2021年8月), 4095-4109. DOI : http://dx.doi.org/10.1007/s00371-021-02283-3
[215] Sakorn Mekruksavanich 和 Anuchit Jitpattanakul. 2021. 带有RNN的深度卷积神经网络，用于使用腕戴式可穿戴传感器数据进行复杂活动识别. *Electronics* 10, 14 (2021年7月), 1685. DOI : http://dx.doi.org/ 10.3390/electronics 10141685
[216] Ling Chen, Xiaozè Liu, Liangying Peng, 和 Menghan Wu. 2021. 基于深度学习的使用可穿戴设备进行的多模态复杂人体活动识别. *Applied Intelligence* 51, 6 (2021年6月), 4029-4042. DOI : http://dx.doi.org/ 10.1007/s10489-020-02005-7
[217] Sakorn Mekruksavanich 和 Anuchit Jitpattanakul. 2021. 使用智能手机数据进行智能家居中基于传感器的人体活动识别的LSTM网络. *Sensors* 21, 5 (2021年2月), 1636. DOI : http://dx.doi.org/10.3390/s21051636
[218] Sakorn Mekruksavanich 和 Anuchit Jitpattanakul. 2021. 基于使用可穿戴传感器进行人体活动识别的生物特征用户识别：使用深度学习模型的实验. *Electronics* 10, 3 (2021年1月), 308. DOI : http://dx.doi.org/10.3390/electronics10030308
[219] Ohoud Nafea, Wadood Abdul, Ghulam Muhammad, 和 Mansour Alsulaiman. 2021. 具有时空深度学习的基于传感器的人体活动识别. *Sensors* 21, 6 (2021年3月), 2141. DOI : http://dx.doi.org/10.3390/ s21062141
[220] Satya P. Singh, Madan Kumar Sharma, Aime Lay-Ekuakille, Deepak Gangwar, 和 Sukrit Gupta. 2021. 具有自注意力机制的深度ConvLSTM，用于使用可穿戴传感器进行人体活动解码. *IEEE Sensors Journal* 21, 6 (2021年3月), 8575-8582. DOI : http://dx.doi.org/10.1109/JSEN.2020.3045135 arXiv:2005.00698
[221] Xing Wang, Lei Zhang, Wenbo Huang, Shuoyuan Wang, Hao Wu, Jun He, 和 Aiguo Song. 2022. 具有可调速度-精度权衡的深度卷积网络，用于使用可穿戴设备进行人体活动识别. *IEEE Transactions on Instrumentation and Measurement* 71 (2022), 1-12. DOI : http://dx.doi.org/10.1109/TIM.2021.3132088
[222] Shige Xu, Lei Zhang, Wenbo Huang, Hao Wu, 和 Aiguo Song. 2022. 用于使用可穿戴传感器进行多模态人体活动识别的可变形卷积网络. *IEEE Transactions on Instrumentation and Measurement* 71 (2022), 1-14. DOI : http://dx.doi.org/10.1109/TIM.2022.3158427
[223] Jifeng Dai, Haozhi Qi, Yuwen Xiong, Yi Li, Guodong Zhang, Han Hu, 和 Yichen Wei. 2017. 可变形卷积网络. 在 *2017 IEEE国际计算机视觉会议 (ICCV'17)* 中. 764-773. DOI : http://dx.doi.org/10. 1109/ICCV.2017.89 arXiv:1703.06211
[224] Michael A. Wulder, Joanne C. White, Samuel N. Goward, Jeffrey G. Masek, James R. Irons, Martin Herold, Warren B. Cohen, Thomas R. Loveland, 和 Curtis E. Woodcock. 2008. 陆地卫星连续性：土地覆盖监测的问题和机遇. *Remote Sensing of Environment* 112, 3 (2008年3月), 955-969. DOI : http://dx.doi.org/10.1016/j.rse. 2007.07.004
[225] William Emery 和 Adriano Camps. 2017. 基本电磁概念及其在光学传感器中的应用. 在 *卫星遥感导论*, William Emery 和 Adriano Camps (编) 中. Elsevier, 第2章, 43-83. DOI : http://dx.doi.org/10.1016/B978-0-12-809254-5.00002-6
[226] Noel Gorelick, Matt Hancher, Mike Dixon, Simon Ilyushchenko, David Thau, 和 Rebecca Moore. 2017. 谷歌地球引擎：面向所有人的行星尺度地理空间分析. *Remote Sensing of Environment* 202 (2017年12月), 18-27. DOI : http://dx.doi.org/10.1016/j.rse.2017.06.031
[227] Grégory Giuliani, Bruno Chatenoux, Andrea De Bono, Denisa Rodila, Jean-Philippe Richard, Karin Allenbach, Hy Dao, 和 Pascal Peduzzi. 2017. 构建地球观测数据立方体：从瑞士数据立方体 (SDC) 中获得的关于生成分析就绪数据 (ARD) 的经验教训. *Big Earth Data* 1, 1-2 (2017年12月), 100-117. DOI : http://dx.doi.org/10. 1080/20964471.2017.1398903
[228] Adam Lewis, Simon Oliver, Leo Lymburner, Ben Evans, Lesley Wyborn, Norman Mueller, Gregory Raevksi, Jeremy Hooke, Rob Woodcock, Joshua Sixsmith, 等. 2017. 澳大利亚地球科学数据立方体——基础和经验教训. *Remote Sensing of Environment* 202 (2017), 276-292.
[229] Dino Ienco, Yawogan Jean Eudes Gbodjo, Roberto Interdonato, 和 Raffaele Gaetano. 2020. 用于具有空间解释的基于对象的卫星图像时间序列数据的注意力弱监督土地覆盖制图. *arXiv* (2020), $1-12$. arXiv:2004.14672
[230] Vivien Sainte Fare Garnot, Loic Landrieu, Sebastien Giordano, 和 Nesrine Chehata. 2020. 具有像素集编码器和时间自注意力的卫星图像时间序列分类. 在 *2020 IEEE/CVF计算机视觉与模式识别会议 (CVPR'20)* 中. IEEE, 12322-12331. DOI : http://dx.doi.org/10.1109/CVPR42600.2020.01234
[231] Anurag Kulshrestha, Ling Chang, 和 Alfred Stein. 2022. 使用LSTM进行与沉降坑相关的异常检测和InSAR形变时间序列的分类. *IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing* 15 (2022), 4559-4570. DOI : http://dx.doi.org/10.1109/JSTARS.2022.3180994


[232] Yifang Ban, Puzhao Zhang, Andrea Nascetti, Alexandre R. Bevington, 和 Michael A. Wulder. 2020. 利用Sentinel-1 SAR时间序列和深度学习进行近实时野火蔓延监测 (Near real-time wildfire progression monitoring with Sentinel-1 SAR time series and deep learning). Scientific Reports 10, 1 (2020年12月), 1322. DOI : http://dx.doi.org/10.1038/s41598-019-56967-x

[233] C. Rambour, N. Audebert, E. Koeniguer, B. Le Saux, M. Crucianu, 和 M. Datcu. 2020. 光学和SAR图像时间序列中的洪水检测 (Flood detection in time series of optical and SAR images). International Archives of Photogrammetry, Remote Sensing, \& Spatial Information Sciences XLIII-B2-2, B2 (2020年8月), 1343-1346. DOI : http://dx.doi.org/10.5194/isprs-archives-XLIII-B2-2020-1343-2020
[234] G. Kamdem De Teyou, Y. Tarabalka, I. Manighetti, R. Almar, 和 S. Tripodi. 2020. 用于自动提取时间序列光学卫星图像特征的深度神经网络 (Deep neural networks for automatic extraction of features in time series optical satellite images). International Archives of Photogrammetry, Remote Sensing, \& Spatial Information Sciences 43 (2020), 1529-1535.
[235] Bruno Menini Matosak, Leila Maria Garcia Fonseca, Evandro Carrijo Taquary, Raian Vargas Maretto, Hugo Do Nascimento Bendini, 和 Marcos Adami. 2022. 基于混合深度学习架构和中等空间分辨率卫星时间序列的塞拉多地区森林砍伐制图 (Mapping deforestation in Cerrado based on hybrid deep learning architecture and medium spatial resolution satellite time series). Remote Sensing 14, 1 (2022), 1-22. DOI : http://dx.doi.org/10. 3390/rs14010209
[236] Dinh Ho Tong Minh, Dino Ienco, Raffaele Gaetano, Nathalie Lalande, Emile Ndikumana, Faycal Osman, 和 Pierre Maurel. 2018. 用于冬季植被质量制图的深度循环神经网络，通过多时相SAR Sentinel1 (Deep recurrent neural networks for winter vegetation quality mapping via multitemporal SAR Sentinel1). IEEE Geoscience and Remote Sensing Letters 15, 3 (2018年3月), 464-468. DOI : http://dx.doi.org/10.1109/LGRS. 2018. 2794581
[237] Pia Labenski, Michael Ewald, Sebastian Schmidtlein, 和 Fabian Ewald Fassnacht. 2022. 基于森林样地照片和使用深度学习的卫星时间序列的地表燃料类型分类 (Classifying surface fuel types based on forest stand photographs and satellite time series using deep learning). International Journal of Applied Earth Observation and Geoinformation 109 (2022年5月), 102799. DOI : http://dx.doi.org/10.1016/j.ijag.2022.102799
[238] Krishna Rao, A. Park Williams, Jacqueline Fortin Flefil, 和 Alexandra G. Konings. 2020. SAR增强的活燃料含水量制图 (SAR-enhanced mapping of live fuel moisture content). Remote Sensing of Environment 245 (2020), 111797. DOI : http://dx.doi.org/10.1016/j.rse. 2020.111797
[239] Liujun Zhu, Geoffrey I. Webb, Marta Yebra, Gianluca Scortechini, Lynn Miller, 和 François Petitjean. 2021. 基于MODIS的活燃料含水量估计：一种深度学习方法 (Live fuel moisture content estimation from MODIS: A deep learning approach). ISPRS Journal of Photogrammetry and Remote Sensing 179 (2021年9月), 81-91. DOI : http://dx.doi.org/10.1016/j.isprsjprs.2021.07.010
[240] Lynn Miller, Liujun Zhu, Marta Yebra, Christoph Rüdiger, 和 Geoffrey I Webb. 2022. 用于活燃料含水量估计的多模态时间CNN (Multi-modal temporal CNNs for live fuel moisture content estimation). Environmental Modelling \& Software 156 (2022年10月), 105467. DOI : http: //dx.doi.org/10.1016/j.envsoft.2022.105467
[241] Jiangjian Xie, Tao Qi, Wanjun Hu, Huaguo Huang, Beibei Chen, 和 Junguo Zhang. 2022. 基于多源遥感数据和集成深度学习模型的活燃料含水量反演 (Retrieval of live fuel moisture content based on multi-source remote sensing data and ensemble deep learning model). Remote Sensing 14, 17 (2022年9月), 4378. DOI : http://dx.doi.org/10.3390/rs14174378
[242] Kamel Lahssini, Florian Teste, Karun Reuel Dayal, Sylvie Durrieu, Dino Ienco, 和 Jean-Matthieu Monnet. 2022. 结合LiDAR指标和Sentinel-2影像，通过神经网络估计复杂森林环境中的基底面积和木材体积 (Combining LiDAR metrics and Sentinel-2 imagery to estimate basal area and wood volume in complex forest environment via neural networks). IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing 15 (2022), 4337-4348. DOI : http://dx.doi.org/10.1109/JSTARS.2022.3175609
[243] Jie Sun, Zulong Lai, Liping Di, Ziheng Sun, Jianbin Tao, 和 Yonglin Shen. 2020. 用于美国玉米带县级玉米产量估计的多层深度学习网络 (Multilevel deep learning network for county-level corn yield estimation in the U.S. Corn Belt). IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing 13 (2020), 5048-5060. DOI : http://dx.doi.org/10.1109/JSTARS.2020.3019046
[244] Zhengtao Li, Guokun Chen, 和 Tianxu Zhang. 2019. 用于多时相多传感器作物分类的时间注意力网络 (Temporal attention networks for multitemporal multisensor crop classification). IEEE Access 7 (2019), 134677-134690. DOI : http://dx.doi.org/10.1109/ACCESS. 2019. 2939152
[245] Zhengtao Li, Gang Zhou, 和 Qiong Song. 2020. 一种用于多时相多传感器作物分类的时间组注意力方法 (A temporal group attention approach for multitemporal multisensor crop classification). Infrared Physics and Technology 105 (2020), 103152. DOI : http://dx.doi.org/10.1016/j.infrared.2019. 103152
[246] Shunping Ji, Chi Zhang, Anjian Xu, Yun Shi, 和 Yulin Duan. 2018. 用于多时相遥感图像作物分类的3D卷积神经网络 (3D convolutional neural networks for crop classification with multi-temporal remote sensing images). Remote Sensing 10, 2 (2018年1月), 75. DOI : http://dx.doi.org/ $10.3390 /$ rs10010075
[247] Jinfan Xu, Yue Zhu, Renhai Zhong, Zhixian Lin, Jialu Xu, Hao Jiang, Jingfeng Huang, Haifeng Li, 和 Tao Lin. 2020. DeepCropMapping：一种多时相深度学习方法，具有改进的空间泛化能力，用于动态玉米和大豆制图 (DeepCropMapping: A multi-temporal deep learning approach with improved spatial generalizability for dynamic corn and soybean mapping). Remote Sensing of Environment 247 (2020年9月), 111946. DOI : http://dx.doi.org/10.1016/j. rse.2020.111946
[248] Valentin Barriere 和 Martin Claverie. 2022. 融合多光谱卫星时间序列与农民作物轮作和当地作物分布的多模态作物类型分类 (Multimodal crop type classification fusing multi-spectral satellite time series with farmers crop rotations and local crop distribution). arXiv preprint:2208.10838 (2022).
[249] Vivien Sainte Fare Garnot 和 Loic Landrieu. 2020. 用于分类卫星图像时间序列的轻量级时间自注意力 (Lightweight temporal self-attention for classifying satellite images time series). In Lecture Notes in Computer Science. Vol. 12588 LNAL Springer International Publishing, 171-181. DOI : http://dx.doi.org/10.1007/978-3-030-65742-0_12
[250] Stella Ofori-Ampofo, Charlotte Pelletier, 和 Stefan Lang. 2021. 使用基于注意力的深度学习从光学和雷达时间序列中进行作物类型制图 (Crop type mapping from optical and radar time series using attention-based deep learning). Remote Sensing 13, 22 (2021年11月), 4668. DOI : http://dx.doi.org/10.3390/ rs13224668

[251] Yuan Yuan 和 Lei Lin. 2021. 用于卫星图像时间序列分类的Transformer的自监督预训练 (Self-Supervised pretraining of transformers for satellite image time series classification). IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing 14 (2021), 474-487. DOI : http://dx. doi.org/10.1109/JSTARS. 2020.3036602
[252] Nicola Di Mauro, Antonio Vergari, Teresa Maria Altomare Basile, Fabrizio G. Ventola, 和 Floriana Esposito. 2017. 用于卫星图像时间序列分类的深度时空表示的端到端学习 (End-to-end learning of deep spatio-temporal representations for satellite image time series classification). In DC@ PKDD/ECML.
[253] Nataliia Kussul, Mykola Lavreniuk, Sergii Skakun, 和 Andrii Shelestov. 2017. 使用遥感数据的土地覆盖和作物类型深度学习分类 (Deep learning classification of land cover and crop types using remote sensing data). IEEE Geoscience and Remote Sensing Letters 14, 5 (2017年5月), 778-782. DOI : http://dx.doi.org/10.1109/LGRS.2017.2681128
[254] Charlotte Pelletier, Geoffrey Webb, 和 François Petitjean. 2019. 用于卫星图像时间序列分类的时间卷积神经网络 (Temporal convolutional neural network for the classification of satellite image time series). Remote Sensing 11, 5 (2019年3月), 523. DOI : http://dx.doi.org/10.3390/ rs11050523
[255] Peng Dou, Huanfeng Shen, Zhiwei Li, 和 Xiaobin Guan. 2021. 使用深度学习和多分类器系统组合的时间序列遥感图像分类框架 (Time series remote sensing image classification framework using combination of deep learning and multiple classifiers system). International Journal of Applied Earth Observation and Geoinformation 103 (2021), 102477. DOI : http://dx.doi.org/10.1016/j.jag.2021.102477


[256] Dino Ienco, Roberto Interdonato, Raffaele Gaetano, and Dinh Ho Tong Minh. 2019. Combining Sentinel-1 and Sentinel-2 satellite image time series for land cover mapping via a multi-source deep learning architecture（结合Sentinel-1和Sentinel-2卫星图像时间序列，通过多源深度学习架构进行土地覆盖制图）. ISPRS Journal of Photogrammetry and Remote Sensing 158 (2019), 11-22. DOI : http://dx.doi.org/10.1016/j.isprsjprs.2019.09.016
[257] Roberto Interdonato, Dino Ienco, Raffaele Gaetano, and Kenji Ose. 2019. DuPLO: A DUal view Point deep Learning architecture for time series classificatiOn（DuPLO：用于时间序列分类的双视角深度学习架构）. ISPRS Journal of Photogrammetry and Remote Sensing 149 (March 2019), 91-104. DOI : http://dx.doi.org/10.1016/j.isprsjprs.2019.01.011
[258] Marc Rußwurm and Marco Körner. 2018. Multi-temporal land cover classification with sequential recurrent encoders（使用序列循环编码器的多时相土地覆盖分类）. ISPRS International Journal of Geo-Information 7, 4 (March 2018), 129. DOI : http://dx.doi.org/10.3390/ ijgi7040129
[259] Andrei Stoian, Vincent Poulain, Jordi Inglada, Victor Poughon, and Dawa Derksen. 2019. Land cover maps production with high resolution satellite image time series and convolutional neural networks: Adaptations and limits for operational systems（利用高分辨率卫星图像时间序列和卷积神经网络生产土地覆盖图：操作系统的适应性和局限性）. Remote Sensing 11, 17 (2019), 1-26. DOI : http://dx.doi.org/10.3390/rs11171986
[260] Dino Ienco, Raffaele Gaetano, Claire Dupaquier, and Pierre Maurel. 2017. Land cover classification via multitemporal spatial data by deep recurrent neural networks（通过深度循环神经网络利用多时相空间数据进行土地覆盖分类）. IEEE Geoscience and Remote Sensing Letters 14, 10 (Oct. 2017), 1685-1689. DOI : http://dx.doi.org/10.1109/LGRS.2017.2728698
[261] Yawogan Jean Eudes Gbodjo, Dino Ienco, Louise Leroux, Roberto Interdonato, Raffaele Gaetano, and Babacar Ndao. 2020. Object-based multi-temporal and multi-source land cover mapping leveraging hierarchical class relationships（利用分层类关系进行基于对象的多时相和多源土地覆盖制图）. Remote Sensing 12, 17 (Aug. 2020), 2814. DOI : http://dx.doi.org/10.3390/rs12172814
[262] Dino Ienco, Raffaele Gaetano, Roberto Interdonato, Kenji Ose, and Dlnh Ho Tong Minh. 2019. Combining Sentinel-1 and Sentinel-2 time series via RNN for object-based land cover classification（通过RNN结合Sentinel-1和Sentinel-2时间序列进行基于对象的土地覆盖分类）. In 2019 IEEE International Geoscience and Remote Sensing Symposium (IGARSS'19). IEEE, 4881-4884. DOI : http://dx.doi.org/10.1109/IGARSS.2019.8898458
[263] Yuan Yuan, Lei Lin, Qingshan Liu, Renlong Hang, and Zeng-Guang Zhou. 2022. SITS-Former: A pre-trained spatio-spectral-temporal representation model for Sentinel-2 time series classification（SITS-Former：用于Sentinel-2时间序列分类的预训练空-谱-时表示模型）. International Journal of Applied Earth Observation and Geoinformation 106 (Feb. 2022), 102651. DOI : http://dx.doi.org/10.1016/j.jag.2021.102651
[264] Mengjia Qiao, Xiaohui He, Xijie Cheng, Panle Li, Haotian Luo, Lehan Zhang, and Zhihui Tian. 2021. Crop yield prediction from multi-spectral, multi-temporal remotely sensed imagery using recurrent 3D convolutional neural networks（使用循环3D卷积神经网络从多光谱、多时相遥感图像中预测作物产量）. International Journal of Applied Earth Observation and Geoinformation 102 (2021), 102436.
[265] Marc Rußwurm and Marco Körner. 2020. Self-attention for raw optical satellite time series classification（用于原始光学卫星时间序列分类的自注意力机制）. ISPRS Journal of Photogrammetry and Remote Sensing 169 (2020), 421-435. DOI : http://dx.doi.org/10.1016/j.isprsjprs.2020.06.006
[266] Devis Tuia, Claudio Persello, and Lorenzo Bruzzone. 2016. Domain adaptation for the classification of remote sensing data: An overview of recent advances（遥感数据分类的领域自适应：近期进展概述）. IEEE Geoscience and Remote Sensing Magazine 4, 2 (2016), 41-57. DOI : http: //dx.doi.org/10.1109/MGRS.2016.2548504
[267] V. Sainte Fare Garnot, Loic Landrieu, Sebastien Giordano, and Nesrine Chehata. 2019. Time-space tradeoff in deep learning models for crop classification on satellite multi-spectral image time series（用于卫星多光谱图像时间序列作物分类的深度学习模型中的时空权衡）. In 2019 IEEE International Geoscience and Remote Sensing Symposium (IGARSS'19). IEEE, 6247-6250.
[268] Hassan Ismail Fawaz, Germain Forestier, Jonathan Weber, Lhassane Idoumghar, and Pierre-Alain Muller. 2019. Deep neural network ensembles for time series classification（用于时间序列分类的深度神经网络集成）. In 2019 International Joint Conference on Neural Networks (IJCNN'19), Vol. 2019. IEEE, 1-6. DOI : http://dx.doi.org/10.1109/IJCNN.2019.8852316
[269] David H. Wolpert. 1992. Stacked generalization（堆叠泛化）. Neural Networks 5, 2 (Jan. 1992), 241-259. DOI : http://dx.doi.org/10. 1016/S0893-6080(05)80023-1
[270] Yoav Freund and Robert E. Schapire. 1996. Experiments with a new boosting algorithm（一种新的boosting算法的实验）. In 13th International Conference on Machine Learning. 148-156.

[271] Cristina Gómez, Joanne C. White, and Michael A. Wulder. 2016. Optical remotely sensed time series data for land cover classification: A review（用于土地覆盖分类的光学遥感时间序列数据：综述）. ISPRS Journal of Photogrammetry and Remote Sensing 116 (2016), 55-72. DOI : http: //dx.doi.org/10.1016/j.isprsjprs.2016.03.008
[272] Xiao Xiang Zhu, Devis Tuia, Lichao Mou, Gui Song Xia, Liangpei Zhang, Feng Xu, and Friedrich Fraundorfer. 2017. Deep learning in remote sensing: A comprehensive review and list of resources（遥感中的深度学习：全面的综述和资源列表）. IEEE Geoscience and Remote Sensing Magazine 5, 4 (2017), 8-36. DOI : http://dx.doi.org/10.1109/MGRS.2017.2762307
[273] Lei Ma, Yu Liu, Xueliang Zhang, Yuanxin Ye, Gaofei Yin, and Brian Alan Johnson. 2019. Deep learning in remote sensing applications: A meta-analysis and review（遥感应用中的深度学习：元分析和综述）. ISPRS Journal of Photogrammetry and Remote Sensing 152 (June 2019), 166-177. DOI : http://dx.doi.org/10.1016/j.isprsjprs.2019.04.015
[274] Qiangqiang Yuan, Huanfeng Shen, Tongwen Li, Zhiwei Li, Shuwen Li, Yun Jiang, Hongzhang Xu, Weiwei Tan, Qianqian Yang, Jiwen Wang, Jianhao Gao, and Liangpei Zhang. 2020. Deep learning in environmental remote sensing: Achievements and challenges（环境遥感中的深度学习：成就与挑战）. Remote Sensing of Environment 241 (May 2020), 111716. DOI : http://dx.doi.org/10.1016/ j.rse.2020.111716
[275] Michel E. D. Chaves, Michelle C. A. Picoli, and Ieda D. Sanches. 2020. Recent applications of Landsat 8/OLI and Sentinel-2/MSI for land use and land cover mapping: A systematic review（Landsat 8/OLI和Sentinel-2/MSI在土地利用和土地覆盖制图中的最新应用：系统综述）. Remote Sensing 12, 18 (sep 2020), 3062. DOI : http://dx.doi.org/10.3390/rs12183062
[276] Waytehad Rose Moskolai, Wahabou Abdou, Albert Dipanda, and Kolyang. 2021. Application of deep learning architectures for satellite image time series prediction: A review（深度学习架构在卫星图像时间序列预测中的应用：综述）. Remote Sensing 13, 23 (Nov. 2021), 4822. DOI : http: //dx.doi.org/10.3390/rs13234822
[277] Jason Lines and Anthony Bagnall. 2015. Time series classification with ensembles of elastic distance measures（使用弹性距离度量集成的的时间序列分类）. Data Mining and Knowledge Discovery 29, 3 (2015), 565-592.
[278] Chang Wei Tan, François Petitjean, and Geoffrey I. Webb. 2020. FastEE: Fast ensembles of elastic distances for time series classification（FastEE：用于时间序列分类的弹性距离的快速集成）. Data Mining and Knowledge Discovery 34, 1 (2020), 231-272.
[279] Matthieu Herrmann and Geoffrey I. Webb. 2021. Amercing: An intuitive, elegant and effective constraint for dynamic time warping（Amercing：一种直观、优雅且有效的动态时间规整约束）. arXiv preprint:2111.13314 (2021).
[280] Anthony Bagnall, Michael Flynn, James Large, Jason Lines, and Matthew Middlehurst. 2020. On the usage and performance of the hierarchical vote collective of transformation-based ensembles version 1.0 (hive-cote v1. 0)（关于基于转换的集成版本1.0（hive-cote v1.0）的分层投票集合的使用和性能）. In International Workshop on Advanced Analytics and Learning on Temporal Data. 3-18.
[281] Matthew Middlehurst, James Large, Michael Flynn, Jason Lines, Aaron Bostrom, and Anthony Bagnall. 2021. HIVE-COTE 2.0: A new meta ensemble for time series classification（HIVE-COTE 2.0：用于时间序列分类的新型元集成）. Machine Learning 110, 11 (2021), 3211-3243.
[282] Anthony Bagnall, Jason Lines, Jon Hills, and Aaron Bostrom. 2015. Time-series classification with COTE: The collective of transformation-based ensembles（使用COTE的时间序列分类：基于转换的集成集合）. IEEE Transactions on Knowledge and Data Engineering 27, 9 (2015), 25222535.


[283] Jason Lines, Sarah Taylor 和 Anthony Bagnall. 2018. 使用 HIVE-COTE 进行时间序列分类：基于转换的集成的分层投票集合 (The hierarchical vote collective of transformation-based ensembles)。ACM Trans. Knowl. Discov. Data 12, 5, Article 52 (October 2018), 35 页. https://doi.org/10.1145/3182382
[284] Jason Lines, Sarah Taylor 和 Anthony Bagnall. 2016. Hive-Cote: 用于时间序列分类的基于转换的集成的分层投票集合 (The hierarchical vote collective of transformationbased ensembles)。In 2016 IEEE 16th International Conference on Data Mining (ICDM'16). IEEE, $1041-1046$.
[285] Rohit J. Kate. 2016. 使用动态时间规整距离 (dynamic time warping distances) 作为特征来改进时间序列分类。Data Mining and Knowledge Discovery 30, 2 (2016), 283-312.
[286] Aaron Bostrom 和 Anthony Bagnall. 2015. 用于多类时间序列分类的二元形状基元变换 (Binary shapelet transform)。In International Conference on Big Data Analytics and Knowledge Discovery. Springer, 257-269.
[287] Patrick Schäfer. 2015. BOSS 关注存在噪声情况下的时间序列分类。Data Mining and Knowledge Discovery 29, 6 (2015), 1505-1530.
[288] Jon Hills, Jason Lines, Edgaras Baranauskas, James Mapp 和 Anthony Bagnall. 2014. 通过形状基元变换 (shapelet transformation) 进行时间序列分类。Data Mining and Knowledge Discovery 28, 4 (2014), 851-881.
[289] Houtao Deng, George Runger, Eugene Tuv 和 Martyanov Vladimir. 2013. 用于分类和特征提取的时间序列森林 (time series forest)。Information Sciences 239 (2013), 142-153.
[290] Mustafa Gokce Baydogan, George Runger 和 Eugene Tuv. 2013. 用于分类时间序列的词袋特征框架 (bag-of-features framework)。IEEE Transactions on Pattern Analysis and Machine Intelligence 35, 11 (2013), 2796-2802.
[291] Angus Dempster, Daniel F. Schmidt 和 Geoffrey I. Webb. 2021. Minirocket：一种用于时间序列分类的非常快速（几乎）确定性变换。In 27th ACM SIGKDD Conference on Knowledge Discovery \& Data Mining. $248-257$.
[292] Chang Wei Tan, Angus Dempster, Christoph Bergmeir 和 Geoffrey I. Webb. 2022. MultiRocket：用于快速有效的时间序列分类的多种池化算子和变换。Data Mining and Knowledge Discovery 36 (June 2022), 1623-1646. DOI : http://dx.doi.org/10.1007/s10618-022-00844-1 arXiv:2102.00457

[293] Angus Dempster, Daniel F. Schmidt 和 Geoffrey I. Webb. 2023. Hydra：用于快速准确的时间序列分类的竞争卷积核 (convolutional kernels)。Data Mining and Knowledge Discovery 37 (2023), 1-27.
[294] Benjamin Lucas, Ahmed Shifaz, Charlotte Pelletier, Lachlan O'Neill, Nayyar Zaidi, Bart Goethals, François Petitjean 和 Geoffrey I. Webb. 2019. 邻近森林 (Proximity forest)：一种有效且可扩展的基于距离的时间序列分类器。Data Mining and Knowledge Discovery 33, 3 (2019), 607-635.
[295] Matthieu Herrmann, Chang Wei Tan, Mahsa Salehi 和 Geoffrey I. Webb. 2023. 邻近森林 2.0 (Proximity forest 2.0)：一种新的有效且可扩展的基于相似性的时间序列分类器。arXiv preprint arXiv:2304.05800 (2023).
[296] Yann LeCun, Léon Bottou, Yoshua Bengio 和 Patrick Haffner. 1998. 基于梯度的学习应用于文档识别。Proceedings of the IEEE 86, 11 (1998), 2278-2324.
[297] Vinod Nair 和 Geoffrey E. Hinton. 2010. 线性整流单元 (Rectified linear units) 改进了受限玻尔兹曼机 (restricted Boltzmann machines)。In ICML.
[298] Salah Hihi 和 Yoshua Bengio. 1995. 用于长期依赖性的分层循环神经网络 (Hierarchical recurrent neural networks)。Advances in Neural Information Processing Systems 8 (1995), 493-499.
[299] Razvan Pascanu, Caglar Gulcehre, Kyunghyun Cho 和 Yoshua Bengio. 2013. 如何构建深度循环神经网络 (deep recurrent neural networks)。arXiv preprint:1312.6026 (2013).
[300] Dzmitry Bahdanau, Kyunghyun Cho 和 Yoshua Bengio. 2014. 通过联合学习对齐和翻译的神经机器翻译 (Neural machine translation)。arXiv preprint:1409.0473 (2014).
[301] Kyunghyun Cho, Bart Van Merriënboer, Caglar Gulcehre, Dzmitry Bahdanau, Fethi Bougares, Holger Schwenk 和 Yoshua Bengio. 2014. 使用 RNN 编码器-解码器学习短语表示以进行统计机器翻译。arXiv preprint:1406.1078 (2014).
[302] Minh-Thang Luong, Hieu Pham 和 Christopher D. Manning. 2015. 基于注意力的神经机器翻译的有效方法。arXiv preprint:1508.04025 (2015).

Received 17 January 2023; revised 15 December 2023; accepted 31 January 2024
